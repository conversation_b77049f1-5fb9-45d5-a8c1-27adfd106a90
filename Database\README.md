# MonoovaPayment Database Setup Guide

## Overview

This guide explains how to set up your database with Entity Framework migrations and stored procedures for the MonoovaPayment Integration project.

## Database Tables

Your project will create **7 tables** from the following entities:

1. **Transactions** - Main transaction records
2. **PayToAgreements** - PayTo agreement records
3. **PayToPaymentInitiations** - PayTo payment initiation records
4. **CardPayments** - Card payment records
5. **CardPaymentRefunds** - Card payment refund records
6. **MAccounts** - Account records
7. **BPAYTransactions** - BPAY transaction records

## Step 1: Create and Run Migrations

### 1.1 Create Initial Migration

Open a terminal in your project root and run:

```bash
dotnet ef migrations add InitialCreate
```

### 1.2 Update Database

Apply the migration to create tables:

```bash
dotnet ef database update
```

### 1.3 Verify Tables Created

Check your SQL Server database to ensure all 7 tables were created with proper relationships and indexes.

## Step 2: Create Stored Procedures

### 2.1 Execute Stored Procedure Scripts

Run the SQL scripts in `Database/StoredProcedures.sql` against your database. You can do this using:

- SQL Server Management Studio (SSMS)
- Azure Data Studio
- Visual Studio SQL Server Object Explorer
- Command line with `sqlcmd`

### 2.2 Available Stored Procedures

The following stored procedures will be created:

1. **sp_GetTransactionsByDateRange** - Get transactions within date range
2. **sp_GetPaymentSummary** - Get payment summary for an account
3. **sp_ProcessBulkPaymentUpdates** - Update multiple payments at once
4. **sp_GetCardPaymentStats** - Get card payment statistics
5. **sp_ArchiveOldTransactions** - Archive old transactions
6. **sp_GetAccountBalanceSummary** - Get account balance summary
7. **sp_GetPayToAgreementSummary** - Get PayTo agreement summary
8. **sp_GetMonthlyTransactionReport** - Get monthly transaction report

## Step 3: Using Stored Procedures in Code

### 3.1 Service Registration

The `StoredProcedureService` is already registered in `Program.cs`:

```csharp
builder.Services.AddScoped<IStoredProcedureService, StoredProcedureService>();
```

### 3.2 Using in Controllers

Example usage in your controllers:

```csharp
[HttpGet("transactions")]
public async Task<IActionResult> GetTransactions(DateTime startDate, DateTime endDate)
{
    var transactions = await _storedProcedureService.GetTransactionsByDateRangeAsync(startDate, endDate);
    return Ok(transactions);
}
```

### 3.3 Available API Endpoints

The `StoredProcedureController` provides these endpoints:

- `GET /api/storedprocedure/transactions?startDate=2024-01-01&endDate=2024-12-31`
- `GET /api/storedprocedure/payment-summary/{accountId}`
- `GET /api/storedprocedure/card-payment-stats?fromDate=2024-01-01&toDate=2024-12-31`
- `GET /api/storedprocedure/account-balance-summary`
- `POST /api/storedprocedure/bulk-payment-updates`
- `POST /api/storedprocedure/archive-transactions`

## Step 4: Testing

### 4.1 Test Database Connection

Run your application and check the logs. You should see:
```
Database connection verified successfully
```

### 4.2 Test API Endpoints

Use tools like:
- Swagger UI (available in development mode)
- Postman
- curl commands

### 4.3 Example curl Commands

```bash
# Get transactions
curl -X GET "https://localhost:7000/api/storedprocedure/transactions?startDate=2024-01-01&endDate=2024-12-31"

# Get account balance summary
curl -X GET "https://localhost:7000/api/storedprocedure/account-balance-summary"
```

## Step 5: Adding Custom Stored Procedures

### 5.1 Create SQL Script

Add your stored procedure to `Database/StoredProcedures.sql` or create a new SQL file.

### 5.2 Add Service Method

Add a method to `StoredProcedureService`:

```csharp
public async Task<List<YourEntity>> YourCustomMethodAsync(parameters)
{
    return await _context.ExecuteStoredProcedureAsync<YourEntity>("sp_YourProcedure", parameters);
}
```

### 5.3 Add Interface Method

Add the method signature to `IStoredProcedureService`.

### 5.4 Add Controller Endpoint

Add an endpoint to `StoredProcedureController` to expose the functionality.

## Troubleshooting

### Connection Issues
- Verify connection string in `appsettings.json`
- Ensure SQL Server is running
- Check firewall settings

### Migration Issues
- Delete `Migrations` folder and recreate if needed
- Use `dotnet ef database drop` to reset database (WARNING: This deletes all data)

### Stored Procedure Issues
- Check SQL syntax
- Verify parameter names match
- Ensure proper permissions on database

## Security Notes

- Always use parameterized queries (already implemented)
- Validate input parameters
- Use appropriate database permissions
- Consider using connection string encryption in production
