using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MonoovaPaymentIntegration.Models
{
    public class PayToAgreement : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string AgreementUniqueReference { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(100)]
        public string PayerName { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(100)]
        public string PayeeName { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(10)]
        public string PayerBSB { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(20)]
        public string PayerAccountNumber { get; set; } = string.Empty;
        
        [MaxLength(200)]
        public string? PayerPayID { get; set; }
        
        [Required]
        [MaxLength(10)]
        public string PayeeBSB { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(20)]
        public string PayeeAccountNumber { get; set; } = string.Empty;
        
        [MaxLength(200)]
        public string? PayeePayID { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Amount { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? MaxAmount { get; set; }
        
        [Required]
        [MaxLength(50)]
        public string AgreementType { get; set; } = string.Empty; // OneOff, Recurring
        
        [MaxLength(50)]
        public string? Frequency { get; set; } // Daily, Weekly, Monthly, etc.
        
        public DateTime? StartDate { get; set; }
        
        public DateTime? EndDate { get; set; }
        
        [Required]
        [MaxLength(50)]
        public string Status { get; set; } = string.Empty; // Pending, Active, Cancelled, Expired
        
        [MaxLength(500)]
        public string? Description { get; set; }
        
        [MaxLength(100)]
        public string? MonoovaAgreementId { get; set; }
        
        public DateTime? AuthorisedAt { get; set; }
        
        [MaxLength(100)]
        public string? ReasonCode { get; set; }
        
        [MaxLength(500)]
        public string? ReasonDescription { get; set; }
        
        // Navigation property for related payment initiations
        public virtual ICollection<PayToPaymentInitiation> PaymentInitiations { get; set; } = new List<PayToPaymentInitiation>();
    }
}
