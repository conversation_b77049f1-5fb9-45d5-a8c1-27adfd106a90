namespace MonoovaPaymentIntegration.DTOs.PayToApi
{
    public class PaymentAgreementResponse
    {
        public string? Status { get; set; }
        public string? StatusDescription { get; set; }
        public string? AgreementUniqueReference { get; set; }
        public string? AgreementId { get; set; }
        public DateTime? CreatedDateTime { get; set; }
        public string? ReasonCode { get; set; }
        public string? ReasonDescription { get; set; }
        public AgreementStatusInfo? AgreementStatus { get; set; }
    }

    public class AgreementStatusInfo
    {
        public string? Status { get; set; }
        public string? StatusDescription { get; set; }
        public DateTime? StatusDateTime { get; set; }
        public string? ReasonCode { get; set; }
        public string? ReasonDescription { get; set; }
    }
}
