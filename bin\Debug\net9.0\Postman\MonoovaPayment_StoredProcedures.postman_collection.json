{"info": {"_postman_id": "12345678-1234-1234-1234-123456789012", "name": "MonoovaPayment - Stored Procedures", "description": "Collection for testing MonoovaPayment stored procedure endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get Transactions by Date Range", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/StoredProcedure/transactions?startDate=2024-01-01&endDate=2024-12-31", "host": ["{{baseUrl}}"], "path": ["api", "StoredProcedure", "transactions"], "query": [{"key": "startDate", "value": "2024-01-01", "description": "Start date in YYYY-MM-DD format"}, {"key": "endDate", "value": "2024-12-31", "description": "End date in YYYY-MM-DD format"}]}, "description": "Get transactions within a specific date range using stored procedure"}, "response": []}, {"name": "Get Payment Summary by Account", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/StoredProcedure/payment-summary/1", "host": ["{{baseUrl}}"], "path": ["api", "StoredProcedure", "payment-summary", "1"]}, "description": "Get payment summary for a specific account ID using stored procedure"}, "response": []}, {"name": "Get Card Payment Statistics", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/StoredProcedure/card-payment-stats?fromDate=2024-01-01&toDate=2024-12-31", "host": ["{{baseUrl}}"], "path": ["api", "StoredProcedure", "card-payment-stats"], "query": [{"key": "fromDate", "value": "2024-01-01", "description": "From date in YYYY-MM-DD format"}, {"key": "toDate", "value": "2024-12-31", "description": "To date in YYYY-MM-DD format"}]}, "description": "Get card payment statistics for a date range using stored procedure"}, "response": []}, {"name": "Get Account Balance Summary", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/StoredProcedure/account-balance-summary", "host": ["{{baseUrl}}"], "path": ["api", "StoredProcedure", "account-balance-summary"]}, "description": "Get account balance summary for all active accounts using stored procedure"}, "response": []}, {"name": "Process Bulk Payment Updates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"paymentIds\": [1, 2, 3, 4, 5],\n  \"status\": \"Completed\"\n}"}, "url": {"raw": "{{baseUrl}}/api/StoredProcedure/bulk-payment-updates", "host": ["{{baseUrl}}"], "path": ["api", "StoredProcedure", "bulk-payment-updates"]}, "description": "Process bulk payment updates using stored procedure"}, "response": []}, {"name": "Archive Old Transactions", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"cutoffDate\": \"2023-12-31T23:59:59Z\"\n}"}, "url": {"raw": "{{baseUrl}}/api/StoredProcedure/archive-transactions", "host": ["{{baseUrl}}"], "path": ["api", "StoredProcedure", "archive-transactions"]}, "description": "Archive old transactions before a cutoff date using stored procedure"}, "response": []}], "variable": [{"key": "baseUrl", "value": "https://localhost:7000", "description": "Base URL for the API (adjust port as needed)"}]}