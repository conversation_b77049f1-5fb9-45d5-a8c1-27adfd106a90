using System.ComponentModel.DataAnnotations;

namespace MonoovaPaymentIntegration.DTOs.CardPaymentsApi
{
    public class CardPaymentRequest
    {
        [Required]
        public string ClientTransactionUniqueReference { get; set; } = string.Empty;
        
        [Required]
        public decimal Amount { get; set; }
        
        [Required]
        public string Currency { get; set; } = "AUD";
        
        [Required]
        public string Description { get; set; } = string.Empty;
        
        [Required]
        public CustomerDetails Customer { get; set; } = new();
        
        [Required]
        public PaymentDetails PaymentDetails { get; set; } = new();
        
        public string? MerchantAccountId { get; set; }
    }

    public class CustomerDetails
    {
        [Required]
        public string FirstName { get; set; } = string.Empty;
        
        [Required]
        public string LastName { get; set; } = string.Empty;
        
        [Required]
        [EmailAddress]
        public string EmailAddress { get; set; } = string.Empty;
        
        [Required]
        public BillingAddress BillingAddress { get; set; } = new();
    }

    public class BillingAddress
    {
        [Required]
        public string Street { get; set; } = string.Empty;
        
        [Required]
        public string Suburb { get; set; } = string.Empty;
        
        [Required]
        public string State { get; set; } = string.Empty;
        
        [Required]
        public string PostalCode { get; set; } = string.Empty;
        
        [Required]
        public string CountryCode { get; set; } = "AU";
    }

    public class PaymentDetails
    {
        [Required]
        public string CardType { get; set; } = string.Empty; // Visa, MasterCard, Amex
        
        // Note: In a real implementation, card details would be tokenized or handled securely
        // This is just for demonstration purposes
        public string? CardToken { get; set; }
    }
}
