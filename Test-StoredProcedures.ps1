# =============================================
# PowerShell Script to Test Stored Procedure Endpoints
# =============================================

param(
    [string]$BaseUrl = "https://localhost:7000"
)

Write-Host "🚀 Testing MonoovaPayment Stored Procedure Endpoints" -ForegroundColor Green
Write-Host "Base URL: $BaseUrl" -ForegroundColor Yellow
Write-Host "=" * 60

# Function to make HTTP requests
function Test-Endpoint {
    param(
        [string]$Method,
        [string]$Url,
        [string]$Body = $null,
        [string]$Description
    )
    
    Write-Host "`n🔍 Testing: $Description" -ForegroundColor Cyan
    Write-Host "   $Method $Url" -ForegroundColor Gray
    
    try {
        $headers = @{
            "Content-Type" = "application/json"
        }
        
        if ($Method -eq "GET") {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $headers -TimeoutSec 30
        } else {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Body $Body -Headers $headers -TimeoutSec 30
        }
        
        Write-Host "   ✅ SUCCESS - Status: 200 OK" -ForegroundColor Green
        
        if ($response -is [array]) {
            Write-Host "   📊 Response: Array with $($response.Count) items" -ForegroundColor Blue
            if ($response.Count -gt 0) {
                Write-Host "   📝 Sample item: $($response[0] | ConvertTo-Json -Compress)" -ForegroundColor Blue
            }
        } elseif ($response) {
            Write-Host "   📝 Response: $($response | ConvertTo-Json -Compress)" -ForegroundColor Blue
        } else {
            Write-Host "   📝 Response: Empty/No content" -ForegroundColor Blue
        }
        
        return $true
    }
    catch {
        Write-Host "   ❌ FAILED - Error: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            Write-Host "   📄 Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        }
        return $false
    }
}

# Test Results Tracking
$testResults = @()

Write-Host "`n🧪 Starting Endpoint Tests..." -ForegroundColor Yellow

# Test 1: Get Account Balance Summary (No parameters - simplest test)
$result1 = Test-Endpoint -Method "GET" -Url "$BaseUrl/api/StoredProcedure/account-balance-summary" -Description "Get Account Balance Summary"
$testResults += @{ Test = "Account Balance Summary"; Result = $result1 }

# Test 2: Get Transactions by Date Range
$startDate = (Get-Date).AddDays(-30).ToString("yyyy-MM-dd")
$endDate = (Get-Date).ToString("yyyy-MM-dd")
$result2 = Test-Endpoint -Method "GET" -Url "$BaseUrl/api/StoredProcedure/transactions?startDate=$startDate&endDate=$endDate" -Description "Get Transactions by Date Range"
$testResults += @{ Test = "Transactions by Date Range"; Result = $result2 }

# Test 3: Get Payment Summary by Account
$result3 = Test-Endpoint -Method "GET" -Url "$BaseUrl/api/StoredProcedure/payment-summary/1" -Description "Get Payment Summary for Account 1"
$testResults += @{ Test = "Payment Summary"; Result = $result3 }

# Test 4: Get Card Payment Statistics
$result4 = Test-Endpoint -Method "GET" -Url "$BaseUrl/api/StoredProcedure/card-payment-stats?fromDate=$startDate&toDate=$endDate" -Description "Get Card Payment Statistics"
$testResults += @{ Test = "Card Payment Statistics"; Result = $result4 }

# Test 5: Process Bulk Payment Updates (POST)
$bulkUpdateBody = @{
    paymentIds = @(1, 2, 3)
    status = "Completed"
} | ConvertTo-Json

$result5 = Test-Endpoint -Method "POST" -Url "$BaseUrl/api/StoredProcedure/bulk-payment-updates" -Body $bulkUpdateBody -Description "Process Bulk Payment Updates"
$testResults += @{ Test = "Bulk Payment Updates"; Result = $result5 }

# Test 6: Archive Old Transactions (POST)
$archiveBody = @{
    cutoffDate = (Get-Date).AddYears(-1).ToString("yyyy-MM-ddTHH:mm:ssZ")
} | ConvertTo-Json

$result6 = Test-Endpoint -Method "POST" -Url "$BaseUrl/api/StoredProcedure/archive-transactions" -Body $archiveBody -Description "Archive Old Transactions"
$testResults += @{ Test = "Archive Transactions"; Result = $result6 }

# Summary Report
Write-Host "`n" + "=" * 60 -ForegroundColor Yellow
Write-Host "📊 TEST SUMMARY REPORT" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Yellow

$passedTests = ($testResults | Where-Object { $_.Result -eq $true }).Count
$totalTests = $testResults.Count

foreach ($test in $testResults) {
    $status = if ($test.Result) { "✅ PASSED" } else { "❌ FAILED" }
    $color = if ($test.Result) { "Green" } else { "Red" }
    Write-Host "   $($test.Test): $status" -ForegroundColor $color
}

Write-Host "`n📈 Overall Results:" -ForegroundColor Yellow
Write-Host "   Passed: $passedTests/$totalTests tests" -ForegroundColor $(if ($passedTests -eq $totalTests) { "Green" } else { "Yellow" })

if ($passedTests -eq $totalTests) {
    Write-Host "`n🎉 ALL TESTS PASSED! Your stored procedures are working perfectly!" -ForegroundColor Green
} elseif ($passedTests -gt 0) {
    Write-Host "`n⚠️  Some tests passed, but there are issues to resolve." -ForegroundColor Yellow
} else {
    Write-Host "`n🚨 ALL TESTS FAILED! Check your setup:" -ForegroundColor Red
    Write-Host "   1. Is the application running on $BaseUrl?" -ForegroundColor Red
    Write-Host "   2. Are the database tables created?" -ForegroundColor Red
    Write-Host "   3. Are the stored procedures created in the database?" -ForegroundColor Red
}

Write-Host "`n🔧 Next Steps:" -ForegroundColor Cyan
if ($passedTests -eq $totalTests) {
    Write-Host "   • Your setup is complete and working!" -ForegroundColor Green
    Write-Host "   • You can now use Postman to test individual endpoints" -ForegroundColor Green
    Write-Host "   • Consider adding more sample data for richer testing" -ForegroundColor Green
} else {
    Write-Host "   • Check the COMPLETE_DEMO_GUIDE.md for setup instructions" -ForegroundColor Yellow
    Write-Host "   • Ensure your application is running: dotnet run" -ForegroundColor Yellow
    Write-Host "   • Verify database connection and stored procedures" -ForegroundColor Yellow
}

Write-Host "`n📚 Documentation:" -ForegroundColor Cyan
Write-Host "   • COMPLETE_DEMO_GUIDE.md - Full setup guide" -ForegroundColor Gray
Write-Host "   • Postman collection - Import for manual testing" -ForegroundColor Gray
Write-Host "   • StoredProcedures.http - VS Code HTTP testing" -ForegroundColor Gray

Write-Host "`n" + "=" * 60 -ForegroundColor Yellow
