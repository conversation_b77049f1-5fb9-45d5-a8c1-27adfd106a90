# MonoovaPayment - Stored Procedures Setup Guide

## What We Created

### 1. **Database Tables (7 tables total)**
Your entities will create these tables:
- `Transactions`
- `PayToAgreements` 
- `PayToPaymentInitiations`
- `CardPayments`
- `CardPaymentRefunds`
- `MAccounts`
- `BPAYTransactions`

### 2. **Stored Procedures**
Created 8 stored procedures in `Database/StoredProcedures.sql`:
- `sp_GetTransactionsByDateRange`
- `sp_GetPaymentSummary`
- `sp_ProcessBulkPaymentUpdates`
- `sp_GetCardPaymentStats`
- `sp_ArchiveOldTransactions`
- `sp_GetAccountBalanceSummary`
- `sp_GetPayToAgreementSummary`
- `sp_GetMonthlyTransactionReport`

### 3. **API Endpoints**
Created `StoredProcedureController` with 6 endpoints:
- `GET /api/StoredProcedure/transactions` - Get transactions by date range
- `GET /api/StoredProcedure/payment-summary/{accountId}` - Get payment summary
- `GET /api/StoredProcedure/card-payment-stats` - Get card payment statistics
- `GET /api/StoredProcedure/account-balance-summary` - Get account balance summary
- `POST /api/StoredProcedure/bulk-payment-updates` - Process bulk updates
- `POST /api/StoredProcedure/archive-transactions` - Archive old transactions

## Setup Steps

### Step 1: Database Setup
1. **Create/Update Database**:
   ```bash
   dotnet ef migrations add StoredProceduresSetup
   dotnet ef database update
   ```

2. **Create Stored Procedures**:
   - Open SQL Server Management Studio or Azure Data Studio
   - Connect to your database
   - Run the scripts from `Database/StoredProcedures.sql`

### Step 2: Run the Application
```bash
dotnet run
```

The application should start on `https://localhost:7000` (or similar port).

### Step 3: Test with Postman

#### Option A: Import Postman Collection
1. Open Postman
2. Import `Postman/MonoovaPayment_StoredProcedures.postman_collection.json`
3. Update the `baseUrl` variable to your application URL
4. Test the endpoints

#### Option B: Use HTTP File
1. Open `StoredProcedures.http` in VS Code
2. Update the `@baseUrl` variable
3. Click "Send Request" on any endpoint

### Step 4: Test Endpoints

#### Simple Test (No Parameters):
```
GET https://localhost:7000/api/StoredProcedure/account-balance-summary
```

#### Test with Parameters:
```
GET https://localhost:7000/api/StoredProcedure/transactions?startDate=2024-01-01&endDate=2024-12-31
```

#### Test POST Endpoint:
```
POST https://localhost:7000/api/StoredProcedure/bulk-payment-updates
Content-Type: application/json

{
  "paymentIds": [1, 2, 3],
  "status": "Completed"
}
```

## How Stored Procedures Work in Your Project

### 1. **Entity Framework Integration**
Your `MonoovaDbContext` now has methods to execute stored procedures:
- `ExecuteStoredProcedureAsync<T>()` - For procedures that return data
- `ExecuteStoredProcedureNonQueryAsync()` - For procedures that don't return data
- `ExecuteRawSqlAsync()` - For custom SQL commands

### 2. **Service Layer**
`StoredProcedureService` provides typed methods:
```csharp
var transactions = await _storedProcedureService.GetTransactionsByDateRangeAsync(startDate, endDate);
```

### 3. **Controller Layer**
`StoredProcedureController` exposes REST API endpoints that call the service methods.

## Example Usage in Code

### Calling a Stored Procedure:
```csharp
// In your service or controller
var startParam = new SqlParameter("@StartDate", startDate);
var endParam = new SqlParameter("@EndDate", endDate);

var transactions = await _context.ExecuteStoredProcedureAsync<Transaction>(
    "sp_GetTransactionsByDateRange", 
    startParam, 
    endParam
);
```

### Adding New Stored Procedures:
1. **Create SQL Script**: Add to `Database/StoredProcedures.sql`
2. **Add Service Method**: Add to `IStoredProcedureService` and `StoredProcedureService`
3. **Add Controller Endpoint**: Add to `StoredProcedureController`
4. **Test**: Add to Postman collection or HTTP file

## Troubleshooting

### Common Issues:

1. **404 Not Found**
   - Check if application is running
   - Verify the URL and port

2. **500 Internal Server Error**
   - Check database connection string
   - Ensure stored procedures exist in database
   - Check application logs

3. **Database Connection Issues**
   - Verify connection string in `appsettings.json`
   - Ensure database server is running

4. **Stored Procedure Not Found**
   - Run the SQL scripts from `Database/StoredProcedures.sql`
   - Check procedure names match exactly

### Checking Logs:
The application will log errors to the console. Watch for:
- Database connection errors
- SQL execution errors
- Parameter validation errors

## Next Steps

1. **Add Sample Data**: Insert test data into your tables
2. **Test All Endpoints**: Use Postman or HTTP file to test each endpoint
3. **Add Authentication**: If needed for your application
4. **Add Validation**: Input validation for parameters
5. **Add Unit Tests**: Test your stored procedure service methods
6. **Monitor Performance**: Check stored procedure execution times

## Benefits of This Approach

1. **Performance**: Stored procedures are pre-compiled and optimized
2. **Security**: Parameters prevent SQL injection
3. **Maintainability**: Business logic centralized in database
4. **Flexibility**: Can handle complex queries and operations
5. **Scalability**: Database-level optimizations and caching
