using Microsoft.AspNetCore.Mvc;
using MonoovaPaymentIntegration.DTOs.PaymentsApi;
using MonoovaPaymentIntegration.Interfaces;
using MonoovaPaymentIntegration.Models;
using MonoovaPaymentIntegration.Data;
using Microsoft.EntityFrameworkCore;

namespace MonoovaPaymentIntegration.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PaymentsController : ControllerBase
    {
        private readonly IMonoovaPaymentsService _paymentsService;
        private readonly MonoovaDbContext _context;
        private readonly ILogger<PaymentsController> _logger;

        public PaymentsController(
            IMonoovaPaymentsService paymentsService,
            MonoovaDbContext context,
            ILogger<PaymentsController> logger)
        {
            _paymentsService = paymentsService;
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Execute a financial transaction
        /// </summary>
        [HttpPost("execute")]
        public async Task<IActionResult> ExecuteTransaction([FromBody] TransactionExecuteRequest request)
        {
            try
            {
                _logger.LogInformation("Executing transaction: {Reference}", request.CallerUniqueReference);

                // Save transaction to database first
                var transaction = new Transaction
                {
                    UniqueReference = request.CallerUniqueReference,
                    TransactionType = DetermineTransactionType(request),
                    Amount = request.Disbursements.Sum(d => d.Amount),
                    Currency = "AUD",
                    Status = "Pending",
                    Description = request.Description,
                    AdditionalData = System.Text.Json.JsonSerializer.Serialize(request)
                };

                _context.Transactions.Add(transaction);
                await _context.SaveChangesAsync();

                // Execute transaction via Monoova API
                var response = await _paymentsService.ExecuteTransactionAsync(request);

                // Update transaction status
                transaction.Status = response.Status ?? "Unknown";
                transaction.ProcessedAt = DateTime.UtcNow;
                transaction.MonoovaTransactionId = response.Transactions?.FirstOrDefault()?.TransactionId;
                
                await _context.SaveChangesAsync();

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing transaction: {Reference}", request.CallerUniqueReference);
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// Validate a financial transaction
        /// </summary>
        [HttpPost("validate")]
        public async Task<IActionResult> ValidateTransaction([FromBody] TransactionExecuteRequest request)
        {
            try
            {
                _logger.LogInformation("Validating transaction: {Reference}", request.CallerUniqueReference);

                var response = await _paymentsService.ValidateTransactionAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating transaction: {Reference}", request.CallerUniqueReference);
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// Get transaction status by unique reference
        /// </summary>
        [HttpGet("status/{uniqueReference}")]
        public async Task<IActionResult> GetTransactionStatus(string uniqueReference)
        {
            try
            {
                _logger.LogInformation("Getting transaction status: {Reference}", uniqueReference);

                // Get from local database first
                var localTransaction = await _context.Transactions
                    .FirstOrDefaultAsync(t => t.UniqueReference == uniqueReference);

                // Get from Monoova API
                var apiResponse = await _paymentsService.GetTransactionStatusAsync(uniqueReference);

                return Ok(new
                {
                    LocalTransaction = localTransaction,
                    MonoovaResponse = apiResponse
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transaction status: {Reference}", uniqueReference);
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// Get transaction status by date range
        /// </summary>
        [HttpGet("status/{startDate}/{endDate}")]
        public async Task<IActionResult> GetTransactionStatusByDate(DateTime startDate, DateTime endDate)
        {
            try
            {
                _logger.LogInformation("Getting transaction status by date: {StartDate} to {EndDate}", startDate, endDate);

                var response = await _paymentsService.GetTransactionStatusByDateAsync(startDate, endDate);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transaction status by date");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// Validate BPAY details
        /// </summary>
        [HttpGet("bpay/validate/{billerCode}")]
        public async Task<IActionResult> ValidateBPAY(string billerCode, [FromQuery] string? custRef = null, [FromQuery] decimal? amount = null)
        {
            try
            {
                _logger.LogInformation("Validating BPAY: {BillerCode}", billerCode);

                var response = await _paymentsService.ValidateBPAYAsync(billerCode, custRef, amount);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating BPAY: {BillerCode}", billerCode);
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// Get BPAY biller details
        /// </summary>
        [HttpGet("bpay/biller/{billerCode}")]
        public async Task<IActionResult> GetBPAYBiller(string billerCode)
        {
            try
            {
                _logger.LogInformation("Getting BPAY biller: {BillerCode}", billerCode);

                var response = await _paymentsService.GetBPAYBillerAsync(billerCode);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting BPAY biller: {BillerCode}", billerCode);
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// Search BPAY billers
        /// </summary>
        [HttpGet("bpay/billers")]
        public async Task<IActionResult> SearchBPAYBillers([FromQuery] string search, [FromQuery] int skip = 0, [FromQuery] int take = 50)
        {
            try
            {
                _logger.LogInformation("Searching BPAY billers: {Search}", search);

                var response = await _paymentsService.SearchBPAYBillersAsync(search, skip, take);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching BPAY billers: {Search}", search);
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// Create mAccount
        /// </summary>
        [HttpPost("maccount")]
        public async Task<IActionResult> CreateMAccount([FromBody] MAccountCreateRequest request)
        {
            try
            {
                _logger.LogInformation("Creating mAccount: {AccountName}", request.AccountName);

                var response = await _paymentsService.CreateMAccountAsync(request);

                // Save to local database
                if (!string.IsNullOrEmpty(response.AccountNumber))
                {
                    var mAccount = new MAccount
                    {
                        AccountNumber = response.AccountNumber,
                        AccountName = response.AccountName ?? request.AccountName,
                        Description = response.Description ?? request.Description,
                        Status = "Active",
                        ParentAccountNumber = request.ParentAccountNumber
                    };

                    _context.MAccounts.Add(mAccount);
                    await _context.SaveChangesAsync();
                }

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating mAccount: {AccountName}", request.AccountName);
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// Get mAccount details
        /// </summary>
        [HttpGet("maccount/{accountNumber}")]
        public async Task<IActionResult> GetMAccount(string accountNumber)
        {
            try
            {
                _logger.LogInformation("Getting mAccount: {AccountNumber}", accountNumber);

                // Get from local database
                var localAccount = await _context.MAccounts
                    .FirstOrDefaultAsync(m => m.AccountNumber == accountNumber);

                // Get from Monoova API
                var apiResponse = await _paymentsService.GetMAccountAsync(accountNumber);

                return Ok(new
                {
                    LocalAccount = localAccount,
                    MonoovaResponse = apiResponse
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting mAccount: {AccountNumber}", accountNumber);
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// Get mAccount financials
        /// </summary>
        [HttpGet("maccount/{accountNumber}/financials")]
        public async Task<IActionResult> GetMAccountFinancials(string accountNumber)
        {
            try
            {
                _logger.LogInformation("Getting mAccount financials: {AccountNumber}", accountNumber);

                var response = await _paymentsService.GetMAccountFinancialsAsync(accountNumber);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting mAccount financials: {AccountNumber}", accountNumber);
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// List all mAccounts
        /// </summary>
        [HttpGet("maccount")]
        public async Task<IActionResult> ListMAccounts()
        {
            try
            {
                _logger.LogInformation("Listing mAccounts");

                // Get from local database
                var localAccounts = await _context.MAccounts.ToListAsync();

                // Get from Monoova API
                var apiResponse = await _paymentsService.ListMAccountsAsync();

                return Ok(new
                {
                    LocalAccounts = localAccounts,
                    MonoovaResponse = apiResponse
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing mAccounts");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// Get uncleared funds
        /// </summary>
        [HttpGet("reports/uncleared-funds")]
        public async Task<IActionResult> GetUnclearedFunds([FromQuery] DateTime startDate, [FromQuery] DateTime? endDate = null, [FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 50)
        {
            try
            {
                _logger.LogInformation("Getting uncleared funds");

                var response = await _paymentsService.GetUnclearedFundsAsync(startDate, endDate, pageNumber, pageSize);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting uncleared funds");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        private string DetermineTransactionType(TransactionExecuteRequest request)
        {
            if (request.Disbursements.Any(d => d.BPAY != null))
                return "BPAY";
            if (request.Disbursements.Any(d => d.NPP != null))
                return "NPP";
            if (request.Disbursements.Any(d => d.BankAccount != null))
                return "DirectCredit";
            if (request.Disbursements.Any(d => d.MAccount != null))
                return "MAccountTransfer";
            
            return "Unknown";
        }
    }
}
