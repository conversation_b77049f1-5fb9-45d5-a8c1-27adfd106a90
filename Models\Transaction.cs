using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MonoovaPaymentIntegration.Models
{
    public class Transaction : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string UniqueReference { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(50)]
        public string TransactionType { get; set; } = string.Empty; // DirectCredit, DirectDebit, NPP, BPAY, etc.
        
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }
        
        [Required]
        [MaxLength(3)]
        public string Currency { get; set; } = "AUD";
        
        [Required]
        [MaxLength(50)]
        public string Status { get; set; } = string.Empty; // Pending, Complete, Failed, etc.
        
        [MaxLength(500)]
        public string? Description { get; set; }
        
        [MaxLength(100)]
        public string? SourceAccountNumber { get; set; }
        
        [MaxLength(100)]
        public string? DestinationAccountNumber { get; set; }
        
        [MaxLength(10)]
        public string? BSB { get; set; }
        
        [MaxLength(200)]
        public string? PayID { get; set; }
        
        [MaxLength(100)]
        public string? BillerCode { get; set; }
        
        [MaxLength(100)]
        public string? CustomerReferenceNumber { get; set; }
        
        public DateTime? ProcessedAt { get; set; }
        
        [MaxLength(1000)]
        public string? ErrorMessage { get; set; }
        
        [MaxLength(100)]
        public string? MonoovaTransactionId { get; set; }
        
        // JSON field for storing additional transaction data
        public string? AdditionalData { get; set; }
    }
}
