using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MonoovaPaymentIntegration.Models
{
    public class CardPaymentRefund : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string RefundUniqueReference { get; set; } = string.Empty;
        
        [Required]
        public int CardPaymentId { get; set; }
        
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal RefundAmount { get; set; }
        
        [Required]
        [MaxLength(3)]
        public string Currency { get; set; } = "AUD";
        
        [Required]
        [MaxLength(50)]
        public string Status { get; set; } = string.Empty; // Pending, Complete, Failed
        
        [MaxLength(500)]
        public string? RefundReason { get; set; }
        
        public DateTime? ProcessedAt { get; set; }
        
        [MaxLength(1000)]
        public string? ErrorMessage { get; set; }
        
        [MaxLength(100)]
        public string? GatewayRefundId { get; set; }
        
        // Navigation property
        [ForeignKey("CardPaymentId")]
        public virtual CardPayment CardPayment { get; set; } = null!;
    }
}
