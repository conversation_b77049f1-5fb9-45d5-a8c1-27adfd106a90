using System.ComponentModel.DataAnnotations;

namespace MonoovaPaymentIntegration.DTOs.PaymentsApi
{
    public class MAccountCreateRequest
    {
        [Required]
        public string AccountName { get; set; } = string.Empty;
        
        public string? Description { get; set; }
        
        public string? ParentAccountNumber { get; set; }
        
        public Dictionary<string, object>? Options { get; set; }
    }

    public class MAccountCreateResponse
    {
        public string? DurationMs { get; set; }
        public string? Status { get; set; }
        public string? StatusDescription { get; set; }
        public string? AccountNumber { get; set; }
        public string? AccountName { get; set; }
        public string? Description { get; set; }
        public DateTime? CreatedDateTime { get; set; }
    }
}
