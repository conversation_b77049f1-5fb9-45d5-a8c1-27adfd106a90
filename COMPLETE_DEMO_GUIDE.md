# 🚀 COMPLETE DEMO: MonoovaPayment Stored Procedures

## 📋 What You'll Get

### **7 Database Tables**
1. `Transactions` - Main transaction records
2. `PayToAgreements` - PayTo agreement records  
3. `PayToPaymentInitiations` - PayTo payment initiations
4. `CardPayments` - Card payment records
5. `CardPaymentRefunds` - Card payment refunds
6. `MAccounts` - Account information
7. `BPAYTransactions` - BPAY transaction records

### **8 Stored Procedures**
1. `sp_GetTransactionsByDateRange` - Get transactions by date
2. `sp_GetPaymentSummary` - Get payment summary by account
3. `sp_ProcessBulkPaymentUpdates` - Bulk update payments
4. `sp_GetCardPaymentStats` - Card payment statistics
5. `sp_ArchiveOldTransactions` - Archive old transactions
6. `sp_GetAccountBalanceSummary` - Account balance summary
7. `sp_GetPayToAgreementSummary` - PayTo agreement summary
8. `sp_GetMonthlyTransactionReport` - Monthly transaction report

### **6 API Endpoints**
- GET `/api/StoredProcedure/transactions` - Get transactions by date range
- GET `/api/StoredProcedure/payment-summary/{accountId}` - Get payment summary
- GET `/api/StoredProcedure/card-payment-stats` - Get card payment stats
- GET `/api/StoredProcedure/account-balance-summary` - Get account balance
- POST `/api/StoredProcedure/bulk-payment-updates` - Process bulk updates
- POST `/api/StoredProcedure/archive-transactions` - Archive transactions

---

## 🛠️ STEP-BY-STEP DEMO

### **Step 1: Stop Any Running Application**
```bash
# Press Ctrl+C in any running terminal to stop the application
```

### **Step 2: Create Database Tables**
```bash
# Navigate to your project directory
cd "D:\Projects\Samples\MonoovaPayment\MonoovaPaymentIntegration"

# Create migration
dotnet ef migrations add InitialCreate

# Update database (creates all 7 tables)
dotnet ef database update
```

### **Step 3: Create Stored Procedures**
1. **Open SQL Server Management Studio** or **Azure Data Studio**
2. **Connect to your database** using connection string from `appsettings.json`
3. **Run this SQL script** (copy and paste):

```sql
-- =============================================
-- CREATE ALL 8 STORED PROCEDURES
-- =============================================

-- 1. Get Transactions by Date Range
CREATE OR ALTER PROCEDURE sp_GetTransactionsByDateRange
    @StartDate DATETIME,
    @EndDate DATETIME
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        Id, UniqueReference, Amount, Currency, Status, Description,
        CreatedAt, UpdatedAt, CreatedBy, UpdatedBy
    FROM Transactions
    WHERE CreatedAt >= @StartDate AND CreatedAt <= @EndDate
    ORDER BY CreatedAt DESC;
END
GO

-- 2. Get Payment Summary by Account
CREATE OR ALTER PROCEDURE sp_GetPaymentSummary
    @AccountId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        @AccountId as AccountId,
        m.AccountNumber,
        ISNULL(SUM(t.Amount), 0) as TotalAmount,
        COUNT(t.Id) as TransactionCount,
        MAX(t.CreatedAt) as LastTransactionDate
    FROM MAccounts m
    LEFT JOIN Transactions t ON m.Id = @AccountId
    WHERE m.Id = @AccountId
    GROUP BY m.AccountNumber;
END
GO

-- 3. Get Account Balance Summary
CREATE OR ALTER PROCEDURE sp_GetAccountBalanceSummary
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        Id as AccountId,
        AccountNumber,
        Balance as CurrentBalance,
        AvailableBalance,
        UnclearedFunds as PendingAmount
    FROM MAccounts
    ORDER BY AccountNumber;
END
GO

-- 4. Get Card Payment Statistics
CREATE OR ALTER PROCEDURE sp_GetCardPaymentStats
    @FromDate DATETIME,
    @ToDate DATETIME
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        PaymentMethod,
        SUM(Amount) as TotalAmount,
        COUNT(CASE WHEN Status = 'Completed' THEN 1 END) as SuccessfulCount,
        COUNT(CASE WHEN Status = 'Failed' THEN 1 END) as FailedCount,
        AVG(Amount) as AverageAmount
    FROM CardPayments
    WHERE CreatedAt >= @FromDate AND CreatedAt <= @ToDate
    GROUP BY PaymentMethod
    ORDER BY TotalAmount DESC;
END
GO

-- 5. Process Bulk Payment Updates
CREATE OR ALTER PROCEDURE sp_ProcessBulkPaymentUpdates
    @PaymentIds NVARCHAR(MAX),
    @Status NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @UpdateCount INT = 0;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        UPDATE CardPayments 
        SET Status = @Status, UpdatedAt = GETUTCDATE()
        WHERE Id IN (SELECT value FROM STRING_SPLIT(@PaymentIds, ','));
        
        SET @UpdateCount = @@ROWCOUNT;
        COMMIT TRANSACTION;
        SELECT @UpdateCount as UpdatedRecords;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END
GO

-- 6. Archive Old Transactions
CREATE OR ALTER PROCEDURE sp_ArchiveOldTransactions
    @CutoffDate DATETIME
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ArchivedCount INT = 0;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Create archive table if it doesn't exist
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='TransactionsArchive' AND xtype='U')
        BEGIN
            SELECT * INTO TransactionsArchive FROM Transactions WHERE 1=0;
        END
        
        -- Move old transactions to archive
        INSERT INTO TransactionsArchive
        SELECT * FROM Transactions WHERE CreatedAt < @CutoffDate;
        
        SET @ArchivedCount = @@ROWCOUNT;
        
        -- Delete archived transactions from main table
        DELETE FROM Transactions WHERE CreatedAt < @CutoffDate;
        
        COMMIT TRANSACTION;
        SELECT @ArchivedCount as ArchivedRecords;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END
GO

-- 7. Get PayTo Agreement Summary
CREATE OR ALTER PROCEDURE sp_GetPayToAgreementSummary
    @AgreementId INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        a.Id, a.AgreementUniqueReference, a.PayerName, a.Amount, a.MaxAmount, a.Status, a.CreatedAt,
        COUNT(p.Id) as InitiationCount, SUM(p.Amount) as TotalInitiatedAmount
    FROM PayToAgreements a
    LEFT JOIN PayToPaymentInitiations p ON a.Id = p.PayToAgreementId
    WHERE (@AgreementId IS NULL OR a.Id = @AgreementId)
    GROUP BY a.Id, a.AgreementUniqueReference, a.PayerName, a.Amount, a.MaxAmount, a.Status, a.CreatedAt
    ORDER BY a.CreatedAt DESC;
END
GO

-- 8. Get Monthly Transaction Report
CREATE OR ALTER PROCEDURE sp_GetMonthlyTransactionReport
    @Year INT,
    @Month INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartDate DATETIME = DATEFROMPARTS(@Year, @Month, 1);
    DECLARE @EndDate DATETIME = EOMONTH(@StartDate);
    
    SELECT 
        DAY(CreatedAt) as Day, COUNT(*) as TransactionCount, SUM(Amount) as TotalAmount,
        AVG(Amount) as AverageAmount,
        COUNT(CASE WHEN Status = 'Completed' THEN 1 END) as SuccessfulCount,
        COUNT(CASE WHEN Status = 'Failed' THEN 1 END) as FailedCount
    FROM Transactions
    WHERE CreatedAt >= @StartDate AND CreatedAt <= @EndDate
    GROUP BY DAY(CreatedAt)
    ORDER BY DAY(CreatedAt);
END
GO

PRINT 'All 8 stored procedures created successfully!'
```

### **Step 4: Add Sample Data (Optional)**
```sql
-- Insert sample data for testing
INSERT INTO MAccounts (AccountNumber, Balance, AvailableBalance, UnclearedFunds, CreatedAt)
VALUES 
    ('ACC001', 1000.00, 950.00, 50.00, GETUTCDATE()),
    ('ACC002', 2500.00, 2400.00, 100.00, GETUTCDATE()),
    ('ACC003', 500.00, 500.00, 0.00, GETUTCDATE());

INSERT INTO Transactions (UniqueReference, Amount, Currency, Status, Description, CreatedAt)
VALUES 
    ('TXN001', 100.50, 'AUD', 'Completed', 'Test Transaction 1', GETUTCDATE()),
    ('TXN002', 250.00, 'AUD', 'Completed', 'Test Transaction 2', GETUTCDATE()),
    ('TXN003', 75.25, 'AUD', 'Failed', 'Test Transaction 3', GETUTCDATE());

INSERT INTO CardPayments (TransactionUniqueReference, Amount, PaymentMethod, Status, CreatedAt)
VALUES 
    ('CP001', 100.50, 'Visa', 'Completed', GETUTCDATE()),
    ('CP002', 250.00, 'Mastercard', 'Completed', GETUTCDATE()),
    ('CP003', 75.25, 'Visa', 'Failed', GETUTCDATE());

PRINT 'Sample data inserted successfully!'
```

### **Step 5: Start the Application**
```bash
dotnet run
```

**Expected Output:**
```
info: Microsoft.Hosting.Lifetime[14]
      Now listening on: https://localhost:7000
info: Microsoft.Hosting.Lifetime[0]
      Application started. Press Ctrl+C to shut down.
```

### **Step 6: Test with Postman**

#### **Import Collection:**
1. Open Postman
2. Click **Import**
3. Select file: `Postman/MonoovaPayment_StoredProcedures.postman_collection.json`
4. Update `baseUrl` variable to `https://localhost:7000` (or your port)

#### **Test Endpoints:**

**🟢 Test 1: Get Account Balance Summary (Simplest)**
```
GET https://localhost:7000/api/StoredProcedure/account-balance-summary
```
**Expected Response:**
```json
[
  {
    "accountId": 1,
    "accountNumber": "ACC001",
    "currentBalance": 1000.00,
    "availableBalance": 950.00,
    "pendingAmount": 50.00
  }
]
```

**🟢 Test 2: Get Transactions by Date Range**
```
GET https://localhost:7000/api/StoredProcedure/transactions?startDate=2024-01-01&endDate=2024-12-31
```

**🟢 Test 3: Get Payment Summary**
```
GET https://localhost:7000/api/StoredProcedure/payment-summary/1
```

**🟢 Test 4: Get Card Payment Stats**
```
GET https://localhost:7000/api/StoredProcedure/card-payment-stats?fromDate=2024-01-01&toDate=2024-12-31
```

**🟡 Test 5: Process Bulk Updates (POST)**
```
POST https://localhost:7000/api/StoredProcedure/bulk-payment-updates
Content-Type: application/json

{
  "paymentIds": [1, 2, 3],
  "status": "Completed"
}
```

**🟡 Test 6: Archive Transactions (POST)**
```
POST https://localhost:7000/api/StoredProcedure/archive-transactions
Content-Type: application/json

{
  "cutoffDate": "2023-12-31T23:59:59Z"
}
```

---

## ✅ SUCCESS INDICATORS

### **✅ Database Tables Created**
Check in SQL Server Management Studio:
- You should see 7 tables: `Transactions`, `PayToAgreements`, `PayToPaymentInitiations`, `CardPayments`, `CardPaymentRefunds`, `MAccounts`, `BPAYTransactions`

### **✅ Stored Procedures Created**
Check in SQL Server Management Studio under **Programmability > Stored Procedures**:
- You should see 8 stored procedures starting with `sp_`

### **✅ API Working**
- Application starts without errors
- GET requests return 200 OK (even if empty data)
- POST requests return 200 OK with result counts

### **✅ Sample Response**
```json
{
  "accountId": 1,
  "accountNumber": "ACC001",
  "currentBalance": 1000.00,
  "availableBalance": 950.00,
  "pendingAmount": 50.00
}
```

---

## 🚨 TROUBLESHOOTING

### **❌ Problem: 404 Not Found**
**Solution:** Check if application is running on correct port

### **❌ Problem: 500 Internal Server Error**
**Solution:** 
1. Check database connection string
2. Verify stored procedures exist
3. Check application console for errors

### **❌ Problem: Empty Results**
**Solution:** Add sample data using the SQL script above

### **❌ Problem: Build Errors**
**Solution:** Stop running application first, then build

---

## 🎯 QUICK TEST CHECKLIST

- [ ] Database tables created (7 tables)
- [ ] Stored procedures created (8 procedures)
- [ ] Application starts successfully
- [ ] GET `/account-balance-summary` returns 200 OK
- [ ] GET `/transactions` with date parameters works
- [ ] POST endpoints accept JSON and return results
- [ ] Sample data inserted and retrievable

**🎉 If all checkboxes are ✅, your stored procedures demo is working perfectly!**
