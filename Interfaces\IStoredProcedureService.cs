using Microsoft.Data.SqlClient;
using MonoovaPaymentIntegration.Models;
using MonoovaPaymentIntegration.Services;

namespace MonoovaPaymentIntegration.Interfaces
{
    public interface IStoredProcedureService
    {
        /// <summary>
        /// Get transactions within a specific date range
        /// </summary>
        Task<List<Transaction>> GetTransactionsByDateRangeAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// Get payment summary for a specific account
        /// </summary>
        Task<List<PaymentSummary>> GetPaymentSummaryAsync(int accountId);

        /// <summary>
        /// Process bulk payment updates
        /// </summary>
        Task<int> ProcessBulkPaymentUpdatesAsync(string paymentIds, string status);

        /// <summary>
        /// Get card payment statistics for a date range
        /// </summary>
        Task<List<CardPaymentStats>> GetCardPaymentStatsAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// Archive old transactions before a cutoff date
        /// </summary>
        Task<int> ArchiveOldTransactionsAsync(DateTime cutoffDate);

        /// <summary>
        /// Get account balance summary for all active accounts
        /// </summary>
        Task<List<AccountBalanceSummary>> GetAccountBalanceSummaryAsync();

        /// <summary>
        /// Execute custom SQL with parameters
        /// </summary>
        Task<int> ExecuteCustomSqlAsync(string sql, params SqlParameter[] parameters);
    }
}
