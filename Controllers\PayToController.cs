using Microsoft.AspNetCore.Mvc;
using MonoovaPaymentIntegration.DTOs.PayToApi;
using MonoovaPaymentIntegration.Interfaces;
using MonoovaPaymentIntegration.Models;
using MonoovaPaymentIntegration.Data;
using Microsoft.EntityFrameworkCore;

namespace MonoovaPaymentIntegration.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PayToController : ControllerBase
    {
        private readonly IMonoovaPayToService _payToService;
        private readonly MonoovaDbContext _context;
        private readonly ILogger<PayToController> _logger;

        public PayToController(
            IMonoovaPayToService payToService,
            MonoovaDbContext context,
            ILogger<PayToController> logger)
        {
            _payToService = payToService;
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Create a PayTo payment agreement
        /// </summary>
        [HttpPost("agreements")]
        public async Task<IActionResult> CreatePaymentAgreement([FromBody] PaymentAgreementRequest request)
        {
            try
            {
                _logger.LogInformation("Creating PayTo agreement: {Reference}", request.AgreementUniqueReference);

                // Save agreement to database first
                var agreement = new PayToAgreement
                {
                    AgreementUniqueReference = request.AgreementUniqueReference,
                    PayerName = request.Payer.Name,
                    PayeeName = request.Payee.Name,
                    PayerBSB = request.Payer.BankAccount?.BSB ?? string.Empty,
                    PayerAccountNumber = request.Payer.BankAccount?.AccountNumber ?? string.Empty,
                    PayerPayID = request.Payer.PayID,
                    PayeeBSB = request.Payee.BankAccount?.BSB ?? string.Empty,
                    PayeeAccountNumber = request.Payee.BankAccount?.AccountNumber ?? string.Empty,
                    PayeePayID = request.Payee.PayID,
                    Amount = request.Agreement.Amount,
                    MaxAmount = request.Agreement.MaxAmount,
                    AgreementType = request.Agreement.Type,
                    Frequency = request.Agreement.Frequency,
                    StartDate = request.Agreement.StartDate,
                    EndDate = request.Agreement.EndDate,
                    Status = "Pending",
                    Description = request.Agreement.Description
                };

                _context.PayToAgreements.Add(agreement);
                await _context.SaveChangesAsync();

                // Create agreement via Monoova API
                var response = await _payToService.CreatePaymentAgreementAsync(request);

                // Update agreement status
                agreement.Status = response.Status ?? "Unknown";
                agreement.MonoovaAgreementId = response.AgreementId;
                agreement.ReasonCode = response.ReasonCode;
                agreement.ReasonDescription = response.ReasonDescription;
                
                await _context.SaveChangesAsync();

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating PayTo agreement: {Reference}", request.AgreementUniqueReference);
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// Get PayTo payment agreement details
        /// </summary>
        [HttpGet("agreements/{agreementUniqueReference}")]
        public async Task<IActionResult> GetPaymentAgreement(string agreementUniqueReference)
        {
            try
            {
                _logger.LogInformation("Getting PayTo agreement: {Reference}", agreementUniqueReference);

                // Get from local database
                var localAgreement = await _context.PayToAgreements
                    .Include(a => a.PaymentInitiations)
                    .FirstOrDefaultAsync(a => a.AgreementUniqueReference == agreementUniqueReference);

                // Get from Monoova API
                var apiResponse = await _payToService.GetPaymentAgreementAsync(agreementUniqueReference);

                return Ok(new
                {
                    LocalAgreement = localAgreement,
                    MonoovaResponse = apiResponse
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting PayTo agreement: {Reference}", agreementUniqueReference);
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// Update PayTo payment agreement
        /// </summary>
        [HttpPut("agreements/{agreementUniqueReference}")]
        public async Task<IActionResult> UpdatePaymentAgreement(string agreementUniqueReference, [FromBody] PaymentAgreementRequest request)
        {
            try
            {
                _logger.LogInformation("Updating PayTo agreement: {Reference}", agreementUniqueReference);

                // Update agreement via Monoova API
                var response = await _payToService.UpdatePaymentAgreementAsync(agreementUniqueReference, request);

                // Update local database
                var localAgreement = await _context.PayToAgreements
                    .FirstOrDefaultAsync(a => a.AgreementUniqueReference == agreementUniqueReference);

                if (localAgreement != null)
                {
                    localAgreement.PayerName = request.Payer.Name;
                    localAgreement.PayeeName = request.Payee.Name;
                    localAgreement.Amount = request.Agreement.Amount;
                    localAgreement.MaxAmount = request.Agreement.MaxAmount;
                    localAgreement.AgreementType = request.Agreement.Type;
                    localAgreement.Frequency = request.Agreement.Frequency;
                    localAgreement.StartDate = request.Agreement.StartDate;
                    localAgreement.EndDate = request.Agreement.EndDate;
                    localAgreement.Description = request.Agreement.Description;
                    localAgreement.Status = response.Status ?? localAgreement.Status;
                    localAgreement.ReasonCode = response.ReasonCode;
                    localAgreement.ReasonDescription = response.ReasonDescription;
                    
                    await _context.SaveChangesAsync();
                }

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating PayTo agreement: {Reference}", agreementUniqueReference);
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// Cancel PayTo payment agreement
        /// </summary>
        [HttpDelete("agreements/{agreementUniqueReference}")]
        public async Task<IActionResult> CancelPaymentAgreement(string agreementUniqueReference)
        {
            try
            {
                _logger.LogInformation("Cancelling PayTo agreement: {Reference}", agreementUniqueReference);

                // Cancel agreement via Monoova API
                var response = await _payToService.CancelPaymentAgreementAsync(agreementUniqueReference);

                // Update local database
                var localAgreement = await _context.PayToAgreements
                    .FirstOrDefaultAsync(a => a.AgreementUniqueReference == agreementUniqueReference);

                if (localAgreement != null)
                {
                    localAgreement.Status = "Cancelled";
                    localAgreement.ReasonCode = response.ReasonCode;
                    localAgreement.ReasonDescription = response.ReasonDescription;
                    
                    await _context.SaveChangesAsync();
                }

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling PayTo agreement: {Reference}", agreementUniqueReference);
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// List PayTo payment agreements
        /// </summary>
        [HttpGet("agreements")]
        public async Task<IActionResult> ListPaymentAgreements([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 50)
        {
            try
            {
                _logger.LogInformation("Listing PayTo agreements");

                // Get from local database
                var localAgreements = await _context.PayToAgreements
                    .Include(a => a.PaymentInitiations)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                // Get from Monoova API
                var apiResponse = await _payToService.ListPaymentAgreementsAsync(pageNumber, pageSize);

                return Ok(new
                {
                    LocalAgreements = localAgreements,
                    MonoovaResponse = apiResponse
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing PayTo agreements");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// Initiate a PayTo payment
        /// </summary>
        [HttpPost("payments")]
        public async Task<IActionResult> InitiatePayment([FromBody] PaymentInitiationRequest request)
        {
            try
            {
                _logger.LogInformation("Initiating PayTo payment: {Reference}", request.InitiationUniqueReference);

                // Find the agreement
                var agreement = await _context.PayToAgreements
                    .FirstOrDefaultAsync(a => a.AgreementUniqueReference == request.AgreementUniqueReference);

                if (agreement == null)
                {
                    return BadRequest(new { error = "Payment agreement not found" });
                }

                // Save payment initiation to database first
                var initiation = new PayToPaymentInitiation
                {
                    InitiationUniqueReference = request.InitiationUniqueReference,
                    PayToAgreementId = agreement.Id,
                    Amount = request.Amount,
                    Currency = request.Currency,
                    Status = "Pending",
                    Description = request.Description
                };

                _context.PayToPaymentInitiations.Add(initiation);
                await _context.SaveChangesAsync();

                // Initiate payment via Monoova API
                var response = await _payToService.InitiatePaymentAsync(request);

                // Update payment initiation status
                initiation.Status = response.Status ?? "Unknown";
                initiation.ProcessedAt = response.ProcessedDateTime;
                initiation.MonoovaInitiationId = response.InitiationId;
                initiation.ReasonCode = response.ReasonCode;
                initiation.ReasonDescription = response.ReasonDescription;
                
                await _context.SaveChangesAsync();

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initiating PayTo payment: {Reference}", request.InitiationUniqueReference);
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// Get PayTo payment initiation details
        /// </summary>
        [HttpGet("payments/{initiationUniqueReference}")]
        public async Task<IActionResult> GetPaymentInitiation(string initiationUniqueReference)
        {
            try
            {
                _logger.LogInformation("Getting PayTo payment initiation: {Reference}", initiationUniqueReference);

                // Get from local database
                var localInitiation = await _context.PayToPaymentInitiations
                    .Include(i => i.PayToAgreement)
                    .FirstOrDefaultAsync(i => i.InitiationUniqueReference == initiationUniqueReference);

                // Get from Monoova API
                var apiResponse = await _payToService.GetPaymentInitiationAsync(initiationUniqueReference);

                return Ok(new
                {
                    LocalInitiation = localInitiation,
                    MonoovaResponse = apiResponse
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting PayTo payment initiation: {Reference}", initiationUniqueReference);
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// Cancel PayTo payment initiation
        /// </summary>
        [HttpDelete("payments/{initiationUniqueReference}")]
        public async Task<IActionResult> CancelPaymentInitiation(string initiationUniqueReference)
        {
            try
            {
                _logger.LogInformation("Cancelling PayTo payment initiation: {Reference}", initiationUniqueReference);

                // Cancel payment initiation via Monoova API
                var response = await _payToService.CancelPaymentInitiationAsync(initiationUniqueReference);

                // Update local database
                var localInitiation = await _context.PayToPaymentInitiations
                    .FirstOrDefaultAsync(i => i.InitiationUniqueReference == initiationUniqueReference);

                if (localInitiation != null)
                {
                    localInitiation.Status = "Cancelled";
                    localInitiation.ReasonCode = response.ReasonCode;
                    localInitiation.ReasonDescription = response.ReasonDescription;
                    
                    await _context.SaveChangesAsync();
                }

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling PayTo payment initiation: {Reference}", initiationUniqueReference);
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// List PayTo payment initiations
        /// </summary>
        [HttpGet("payments")]
        public async Task<IActionResult> ListPaymentInitiations([FromQuery] string? agreementUniqueReference = null, [FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 50)
        {
            try
            {
                _logger.LogInformation("Listing PayTo payment initiations");

                // Get from local database
                var query = _context.PayToPaymentInitiations
                    .Include(i => i.PayToAgreement)
                    .AsQueryable();

                if (!string.IsNullOrEmpty(agreementUniqueReference))
                {
                    query = query.Where(i => i.PayToAgreement.AgreementUniqueReference == agreementUniqueReference);
                }

                var localInitiations = await query
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                // Get from Monoova API
                var apiResponse = await _payToService.ListPaymentInitiationsAsync(agreementUniqueReference, pageNumber, pageSize);

                return Ok(new
                {
                    LocalInitiations = localInitiations,
                    MonoovaResponse = apiResponse
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing PayTo payment initiations");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// Get PayTo agreement status history
        /// </summary>
        [HttpGet("agreements/{agreementUniqueReference}/status-history")]
        public async Task<IActionResult> GetAgreementStatusHistory(string agreementUniqueReference)
        {
            try
            {
                _logger.LogInformation("Getting PayTo agreement status history: {Reference}", agreementUniqueReference);

                var response = await _payToService.GetAgreementStatusHistoryAsync(agreementUniqueReference);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting PayTo agreement status history: {Reference}", agreementUniqueReference);
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// Get PayTo payment status history
        /// </summary>
        [HttpGet("payments/{initiationUniqueReference}/status-history")]
        public async Task<IActionResult> GetPaymentStatusHistory(string initiationUniqueReference)
        {
            try
            {
                _logger.LogInformation("Getting PayTo payment status history: {Reference}", initiationUniqueReference);

                var response = await _payToService.GetPaymentStatusHistoryAsync(initiationUniqueReference);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting PayTo payment status history: {Reference}", initiationUniqueReference);
                return StatusCode(500, new { error = ex.Message });
            }
        }
    }
}
