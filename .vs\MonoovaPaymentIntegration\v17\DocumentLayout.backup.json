{"Version": 1, "WorkspaceRootPath": "D:\\Projects\\Samples\\MonoovaPayment\\MonoovaPaymentIntegration\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A4A05CBB-1015-1869-F254-4A1659FB9634}|MonoovaPaymentIntegration.csproj|d:\\projects\\samples\\monoovapayment\\monoovapaymentintegration\\dtos\\cardpaymentsapi\\cardpaymentresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A4A05CBB-1015-1869-F254-4A1659FB9634}|MonoovaPaymentIntegration.csproj|solutionrelative:dtos\\cardpaymentsapi\\cardpaymentresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "CardPaymentResponse.cs", "DocumentMoniker": "D:\\Projects\\Samples\\MonoovaPayment\\MonoovaPaymentIntegration\\DTOs\\CardPaymentsApi\\CardPaymentResponse.cs", "RelativeDocumentMoniker": "DTOs\\CardPaymentsApi\\CardPaymentResponse.cs", "ToolTip": "D:\\Projects\\Samples\\MonoovaPayment\\MonoovaPaymentIntegration\\DTOs\\CardPaymentsApi\\CardPaymentResponse.cs", "RelativeToolTip": "DTOs\\CardPaymentsApi\\CardPaymentResponse.cs", "ViewState": "AgIAAAsAAAAAAAAAAAA8wAoAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T11:53:03.374Z", "EditorCaption": ""}]}]}]}