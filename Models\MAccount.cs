using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MonoovaPaymentIntegration.Models
{
    public class MAccount : BaseEntity
    {
        [Required]
        [MaxLength(16)]
        public string AccountNumber { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(200)]
        public string AccountName { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string? Description { get; set; }
        
        [Required]
        [MaxLength(50)]
        public string Status { get; set; } = string.Empty; // Active, Closed, Suspended
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal Balance { get; set; } = 0;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal AvailableBalance { get; set; } = 0;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnclearedFunds { get; set; } = 0;
        
        public DateTime? LastBalanceUpdate { get; set; }
        
        [MaxLength(100)]
        public string? ParentAccountNumber { get; set; }
        
        // JSON field for storing account options and settings
        public string? AccountOptions { get; set; }
    }
}
