using System.ComponentModel.DataAnnotations;

namespace MonoovaPaymentIntegration.DTOs.PayToApi
{
    public class PaymentAgreementRequest
    {
        [Required]
        public string AgreementUniqueReference { get; set; } = string.Empty;
        
        [Required]
        public PayerDetails Payer { get; set; } = new();
        
        [Required]
        public PayeeDetails Payee { get; set; } = new();
        
        [Required]
        public AgreementDetails Agreement { get; set; } = new();
    }

    public class PayerDetails
    {
        [Required]
        public string Name { get; set; } = string.Empty;
        
        public BankAccountInfo? BankAccount { get; set; }
        public string? PayID { get; set; }
    }

    public class PayeeDetails
    {
        [Required]
        public string Name { get; set; } = string.Empty;
        
        public BankAccountInfo? BankAccount { get; set; }
        public string? PayID { get; set; }
    }

    public class BankAccountInfo
    {
        [Required]
        public string BSB { get; set; } = string.Empty;
        
        [Required]
        public string AccountNumber { get; set; } = string.Empty;
        
        [Required]
        public string AccountName { get; set; } = string.Empty;
    }

    public class AgreementDetails
    {
        public decimal? Amount { get; set; }
        public decimal? MaxAmount { get; set; }
        
        [Required]
        public string Type { get; set; } = string.Empty; // OneOff, Recurring
        
        public string? Frequency { get; set; } // Daily, Weekly, Monthly, etc.
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Description { get; set; }
    }
}
