namespace MonoovaPaymentIntegration.DTOs.CardPaymentsApi
{
    public class CardPaymentResponse
    {
        public string? Status { get; set; }
        public string? StatusDescription { get; set; }
        public string? ClientTransactionUniqueReference { get; set; }
        public string? GatewayTransactionId { get; set; }
        public string? AuthorizationCode { get; set; }
        public decimal? Amount { get; set; }
        public string? Currency { get; set; }
        public DateTime? ProcessedDateTime { get; set; }
        public string? CardType { get; set; }
        public string? CardLastFourDigits { get; set; }
        public string? ErrorMessage { get; set; }
        public string? ErrorCode { get; set; }
    }

    public class CardPaymentRefundRequest
    {
        [Required]
        public string ClientTransactionUniqueReference { get; set; } = string.Empty;
        
        [Required]
        public string RefundUniqueReference { get; set; } = string.Empty;
        
        [Required]
        public decimal RefundAmount { get; set; }
        
        [Required]
        public string RefundReason { get; set; } = string.Empty;
    }

    public class CardPaymentRefundResponse
    {
        public string? Status { get; set; }
        public string? StatusDescription { get; set; }
        public string? RefundUniqueReference { get; set; }
        public string? GatewayRefundId { get; set; }
        public decimal? RefundAmount { get; set; }
        public string? Currency { get; set; }
        public DateTime? ProcessedDateTime { get; set; }
        public string? ErrorMessage { get; set; }
        public string? ErrorCode { get; set; }
    }
}
