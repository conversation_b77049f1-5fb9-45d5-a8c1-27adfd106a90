-- =============================================
-- Stored Procedures for MonoovaPayment Integration
-- =============================================

-- 1. Get Transactions by Date Range
CREATE OR ALTER PROCEDURE sp_GetTransactionsByDateRange
    @StartDate DATETIME,
    @EndDate DATETIME
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        Id,
        UniqueReference,
        Amount,
        Currency,
        Status,
        Description,
        CreatedAt,
        UpdatedAt,
        CreatedBy,
        UpdatedBy
    FROM Transactions
    WHERE CreatedAt >= @StartDate 
      AND CreatedAt <= @EndDate
    ORDER BY CreatedAt DESC;
END
GO

-- 2. Get Payment Summary by Account
CREATE OR ALTER PROCEDURE sp_GetPaymentSummary
    @AccountId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        @AccountId as AccountId,
        m.AccountNumber,
        ISNULL(SUM(t.Amount), 0) as TotalAmount,
        COUNT(t.Id) as TransactionCount,
        MAX(t.CreatedAt) as LastTransactionDate
    FROM MAccounts m
    LEFT JOIN Transactions t ON m.Id = @AccountId
    WHERE m.Id = @AccountId
    GROUP BY m.AccountNumber;
END
GO

-- 3. Process Bulk Payment Updates
CREATE OR ALTER PROCEDURE sp_ProcessBulkPaymentUpdates
    @PaymentIds NVARCHAR(MAX),
    @Status NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @UpdateCount INT = 0;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Update CardPayments
        UPDATE CardPayments 
        SET Status = @Status,
            UpdatedAt = GETUTCDATE()
        WHERE Id IN (SELECT value FROM STRING_SPLIT(@PaymentIds, ','));
        
        SET @UpdateCount = @@ROWCOUNT;
        
        COMMIT TRANSACTION;
        
        SELECT @UpdateCount as UpdatedRecords;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END
GO

-- 4. Get Card Payment Statistics
CREATE OR ALTER PROCEDURE sp_GetCardPaymentStats
    @FromDate DATETIME,
    @ToDate DATETIME
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        PaymentMethod,
        SUM(Amount) as TotalAmount,
        COUNT(CASE WHEN Status = 'Completed' THEN 1 END) as SuccessfulCount,
        COUNT(CASE WHEN Status = 'Failed' THEN 1 END) as FailedCount,
        AVG(Amount) as AverageAmount
    FROM CardPayments
    WHERE CreatedAt >= @FromDate 
      AND CreatedAt <= @ToDate
    GROUP BY PaymentMethod
    ORDER BY TotalAmount DESC;
END
GO

-- 5. Archive Old Transactions
CREATE OR ALTER PROCEDURE sp_ArchiveOldTransactions
    @CutoffDate DATETIME
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ArchivedCount INT = 0;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Create archive table if it doesn't exist
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='TransactionsArchive' AND xtype='U')
        BEGIN
            SELECT * INTO TransactionsArchive FROM Transactions WHERE 1=0;
        END
        
        -- Move old transactions to archive
        INSERT INTO TransactionsArchive
        SELECT * FROM Transactions
        WHERE CreatedAt < @CutoffDate;
        
        SET @ArchivedCount = @@ROWCOUNT;
        
        -- Delete archived transactions from main table
        DELETE FROM Transactions
        WHERE CreatedAt < @CutoffDate;
        
        COMMIT TRANSACTION;
        
        SELECT @ArchivedCount as ArchivedRecords;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END
GO

-- 6. Get Account Balance Summary
CREATE OR ALTER PROCEDURE sp_GetAccountBalanceSummary
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        Id as AccountId,
        AccountNumber,
        Balance as CurrentBalance,
        AvailableBalance,
        UnclearedFunds as PendingAmount
    FROM MAccounts
    WHERE IsActive = 1
    ORDER BY AccountNumber;
END
GO

-- 7. Get PayTo Agreement Summary
CREATE OR ALTER PROCEDURE sp_GetPayToAgreementSummary
    @AgreementId INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        a.Id,
        a.AgreementUniqueReference,
        a.PayerName,
        a.Amount,
        a.MaxAmount,
        a.Status,
        a.CreatedAt,
        COUNT(p.Id) as InitiationCount,
        SUM(p.Amount) as TotalInitiatedAmount
    FROM PayToAgreements a
    LEFT JOIN PayToPaymentInitiations p ON a.Id = p.PayToAgreementId
    WHERE (@AgreementId IS NULL OR a.Id = @AgreementId)
    GROUP BY a.Id, a.AgreementUniqueReference, a.PayerName, a.Amount, a.MaxAmount, a.Status, a.CreatedAt
    ORDER BY a.CreatedAt DESC;
END
GO

-- 8. Get Monthly Transaction Report
CREATE OR ALTER PROCEDURE sp_GetMonthlyTransactionReport
    @Year INT,
    @Month INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartDate DATETIME = DATEFROMPARTS(@Year, @Month, 1);
    DECLARE @EndDate DATETIME = EOMONTH(@StartDate);
    
    SELECT 
        DAY(CreatedAt) as Day,
        COUNT(*) as TransactionCount,
        SUM(Amount) as TotalAmount,
        AVG(Amount) as AverageAmount,
        COUNT(CASE WHEN Status = 'Completed' THEN 1 END) as SuccessfulCount,
        COUNT(CASE WHEN Status = 'Failed' THEN 1 END) as FailedCount
    FROM Transactions
    WHERE CreatedAt >= @StartDate 
      AND CreatedAt <= @EndDate
    GROUP BY DAY(CreatedAt)
    ORDER BY DAY(CreatedAt);
END
GO
