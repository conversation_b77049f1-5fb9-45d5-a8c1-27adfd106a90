{"Version": 1, "WorkspaceRootPath": "D:\\Projects\\Samples\\MonoovaPayment\\MonoovaPaymentIntegration\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A4A05CBB-1015-1869-F254-4A1659FB9634}|MonoovaPaymentIntegration.csproj|d:\\projects\\samples\\monoovapayment\\monoovapaymentintegration\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{A4A05CBB-1015-1869-F254-4A1659FB9634}|MonoovaPaymentIntegration.csproj|solutionrelative:appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{A4A05CBB-1015-1869-F254-4A1659FB9634}|MonoovaPaymentIntegration.csproj|d:\\projects\\samples\\monoovapayment\\monoovapaymentintegration\\controllers\\paymentscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A4A05CBB-1015-1869-F254-4A1659FB9634}|MonoovaPaymentIntegration.csproj|solutionrelative:controllers\\paymentscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A4A05CBB-1015-1869-F254-4A1659FB9634}|MonoovaPaymentIntegration.csproj|d:\\projects\\samples\\monoovapayment\\monoovapaymentintegration\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{A4A05CBB-1015-1869-F254-4A1659FB9634}|MonoovaPaymentIntegration.csproj|solutionrelative:properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{A4A05CBB-1015-1869-F254-4A1659FB9634}|MonoovaPaymentIntegration.csproj|d:\\projects\\samples\\monoovapayment\\monoovapaymentintegration\\monoovapaymentintegration.http||{5703B403-55E7-4C63-8C88-A8F52C7A45C5}", "RelativeMoniker": "D:0:0:{A4A05CBB-1015-1869-F254-4A1659FB9634}|MonoovaPaymentIntegration.csproj|solutionrelative:monoovapaymentintegration.http||{5703B403-55E7-4C63-8C88-A8F52C7A45C5}"}, {"AbsoluteMoniker": "D:0:0:{A4A05CBB-1015-1869-F254-4A1659FB9634}|MonoovaPaymentIntegration.csproj|d:\\projects\\samples\\monoovapayment\\monoovapaymentintegration\\configuration\\monoovasettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A4A05CBB-1015-1869-F254-4A1659FB9634}|MonoovaPaymentIntegration.csproj|solutionrelative:configuration\\monoovasettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A4A05CBB-1015-1869-F254-4A1659FB9634}|MonoovaPaymentIntegration.csproj|d:\\projects\\samples\\monoovapayment\\monoovapaymentintegration\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A4A05CBB-1015-1869-F254-4A1659FB9634}|MonoovaPaymentIntegration.csproj|solutionrelative:program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A4A05CBB-1015-1869-F254-4A1659FB9634}|MonoovaPaymentIntegration.csproj|d:\\projects\\samples\\monoovapayment\\monoovapaymentintegration\\controllers\\bpaytransaction.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A4A05CBB-1015-1869-F254-4A1659FB9634}|MonoovaPaymentIntegration.csproj|solutionrelative:controllers\\bpaytransaction.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A4A05CBB-1015-1869-F254-4A1659FB9634}|MonoovaPaymentIntegration.csproj|d:\\projects\\samples\\monoovapayment\\monoovapaymentintegration\\dtos\\cardpaymentsapi\\cardpaymentresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A4A05CBB-1015-1869-F254-4A1659FB9634}|MonoovaPaymentIntegration.csproj|solutionrelative:dtos\\cardpaymentsapi\\cardpaymentresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 6, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "PaymentsController.cs", "DocumentMoniker": "D:\\Projects\\Samples\\MonoovaPayment\\MonoovaPaymentIntegration\\Controllers\\PaymentsController.cs", "RelativeDocumentMoniker": "Controllers\\PaymentsController.cs", "ToolTip": "D:\\Projects\\Samples\\MonoovaPayment\\MonoovaPaymentIntegration\\Controllers\\PaymentsController.cs", "RelativeToolTip": "Controllers\\PaymentsController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T12:14:17.143Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "launchSettings.json", "DocumentMoniker": "D:\\Projects\\Samples\\MonoovaPayment\\MonoovaPaymentIntegration\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "Properties\\launchSettings.json", "ToolTip": "D:\\Projects\\Samples\\MonoovaPayment\\MonoovaPaymentIntegration\\Properties\\launchSettings.json", "RelativeToolTip": "Properties\\launchSettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-11T12:12:04.457Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "MonoovaPaymentIntegration.http", "DocumentMoniker": "D:\\Projects\\Samples\\MonoovaPayment\\MonoovaPaymentIntegration\\MonoovaPaymentIntegration.http", "RelativeDocumentMoniker": "MonoovaPaymentIntegration.http", "ToolTip": "D:\\Projects\\Samples\\MonoovaPayment\\MonoovaPaymentIntegration\\MonoovaPaymentIntegration.http", "RelativeToolTip": "MonoovaPaymentIntegration.http", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003502|", "WhenOpened": "2025-07-11T12:10:49.116Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "MonoovaSettings.cs", "DocumentMoniker": "D:\\Projects\\Samples\\MonoovaPayment\\MonoovaPaymentIntegration\\Configuration\\MonoovaSettings.cs", "RelativeDocumentMoniker": "Configuration\\MonoovaSettings.cs", "ToolTip": "D:\\Projects\\Samples\\MonoovaPayment\\MonoovaPaymentIntegration\\Configuration\\MonoovaSettings.cs", "RelativeToolTip": "Configuration\\MonoovaSettings.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T12:10:41.602Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "Program.cs", "DocumentMoniker": "D:\\Projects\\Samples\\MonoovaPayment\\MonoovaPaymentIntegration\\Program.cs", "RelativeDocumentMoniker": "Program.cs", "ToolTip": "D:\\Projects\\Samples\\MonoovaPayment\\MonoovaPaymentIntegration\\Program.cs", "RelativeToolTip": "Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T12:10:20.405Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "appsettings.json", "DocumentMoniker": "D:\\Projects\\Samples\\MonoovaPayment\\MonoovaPaymentIntegration\\appsettings.json", "RelativeDocumentMoniker": "appsettings.json", "ToolTip": "D:\\Projects\\Samples\\MonoovaPayment\\MonoovaPaymentIntegration\\appsettings.json", "RelativeToolTip": "appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABcAAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-11T12:09:56.789Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "BPAYTransaction.cs", "DocumentMoniker": "D:\\Projects\\Samples\\MonoovaPayment\\MonoovaPaymentIntegration\\Controllers\\BPAYTransaction.cs", "RelativeDocumentMoniker": "Controllers\\BPAYTransaction.cs", "ToolTip": "D:\\Projects\\Samples\\MonoovaPayment\\MonoovaPaymentIntegration\\Controllers\\BPAYTransaction.cs", "RelativeToolTip": "Controllers\\BPAYTransaction.cs", "ViewState": "AgIAABQAAAAAAAAAAAAwwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T12:14:11.735Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "CardPaymentResponse.cs", "DocumentMoniker": "D:\\Projects\\Samples\\MonoovaPayment\\MonoovaPaymentIntegration\\DTOs\\CardPaymentsApi\\CardPaymentResponse.cs", "RelativeDocumentMoniker": "DTOs\\CardPaymentsApi\\CardPaymentResponse.cs", "ToolTip": "D:\\Projects\\Samples\\MonoovaPayment\\MonoovaPaymentIntegration\\DTOs\\CardPaymentsApi\\CardPaymentResponse.cs", "RelativeToolTip": "DTOs\\CardPaymentsApi\\CardPaymentResponse.cs", "ViewState": "AgIAAAsAAAAAAAAAAAA8wBoAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T11:53:03.374Z"}]}]}]}