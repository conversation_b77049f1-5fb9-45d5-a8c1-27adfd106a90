namespace MonoovaPaymentIntegration.Configuration
{
    public class MonoovaSettings
    {
        public const string SectionName = "MonoovaSettings";
        
        public PaymentsApiSettings PaymentsApi { get; set; } = new();
        public PayToApiSettings PayToApi { get; set; } = new();
        public CardPaymentsApiSettings CardPaymentsApi { get; set; } = new();
    }

    public class PaymentsApiSettings
    {
        public string BaseUrl { get; set; } = string.Empty;
        public string ApiKey { get; set; } = string.Empty;
        public bool IsSandbox { get; set; } = true;
        public int TimeoutSeconds { get; set; } = 30;
    }

    public class PayToApiSettings
    {
        public string BaseUrl { get; set; } = string.Empty;
        public string ApiKey { get; set; } = string.Empty;
        public string ClientId { get; set; } = string.Empty;
        public bool IsSandbox { get; set; } = true;
        public int TimeoutSeconds { get; set; } = 30;
    }

    public class CardPaymentsApiSettings
    {
        public string BaseUrl { get; set; } = string.Empty;
        public string ApiKey { get; set; } = string.Empty;
        public string ClientId { get; set; } = string.Empty;
        public bool IsSandbox { get; set; } = true;
        public int TimeoutSeconds { get; set; } = 30;
    }
}
