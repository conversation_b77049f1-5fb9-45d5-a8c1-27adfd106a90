[{"ContainingType": "MonoovaPaymentIntegration.Controllers.PaymentsController", "Method": "GetBPAYBiller", "RelativePath": "api/Payments/bpay/biller/{billerCode}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "billerCode", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.PaymentsController", "Method": "SearchBPAYBillers", "RelativePath": "api/Payments/bpay/billers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "System.String", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.PaymentsController", "Method": "ValidateBPAY", "RelativePath": "api/Payments/bpay/validate/{billerCode}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "billerCode", "Type": "System.String", "IsRequired": true}, {"Name": "custRef", "Type": "System.String", "IsRequired": false}, {"Name": "amount", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.PaymentsController", "Method": "ExecuteTransaction", "RelativePath": "api/Payments/execute", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "MonoovaPaymentIntegration.DTOs.PaymentsApi.TransactionExecuteRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.PaymentsController", "Method": "CreateMAccount", "RelativePath": "api/Payments/maccount", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "MonoovaPaymentIntegration.DTOs.PaymentsApi.MAccountCreateRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.PaymentsController", "Method": "ListMAccounts", "RelativePath": "api/Payments/maccount", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.PaymentsController", "Method": "GetMAccount", "RelativePath": "api/Payments/maccount/{accountNumber}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "accountNumber", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.PaymentsController", "Method": "GetMAccountFinancials", "RelativePath": "api/Payments/maccount/{accountNumber}/financials", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "accountNumber", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.PaymentsController", "Method": "GetUnclearedFunds", "RelativePath": "api/Payments/reports/uncleared-funds", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.PaymentsController", "Method": "GetTransactionStatusByDate", "RelativePath": "api/Payments/status/{startDate}/{endDate}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.DateTime", "IsRequired": true}, {"Name": "endDate", "Type": "System.DateTime", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.PaymentsController", "Method": "GetTransactionStatus", "RelativePath": "api/Payments/status/{uniqueReference}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "uniqueReference", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.PaymentsController", "Method": "ValidateTransaction", "RelativePath": "api/Payments/validate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "MonoovaPaymentIntegration.DTOs.PaymentsApi.TransactionExecuteRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.PayToController", "Method": "CreatePaymentAgreement", "RelativePath": "api/PayTo/agreements", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "MonoovaPaymentIntegration.DTOs.PayToApi.PaymentAgreementRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.PayToController", "Method": "ListPaymentAgreements", "RelativePath": "api/PayTo/agreements", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.PayToController", "Method": "GetPaymentAgreement", "RelativePath": "api/PayTo/agreements/{agreementUniqueReference}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "agreementUniqueReference", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.PayToController", "Method": "UpdatePaymentAgreement", "RelativePath": "api/PayTo/agreements/{agreementUniqueReference}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "agreementUniqueReference", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "MonoovaPaymentIntegration.DTOs.PayToApi.PaymentAgreementRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.PayToController", "Method": "CancelPaymentAgreement", "RelativePath": "api/PayTo/agreements/{agreementUniqueReference}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "agreementUniqueReference", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.PayToController", "Method": "GetAgreementStatusHistory", "RelativePath": "api/PayTo/agreements/{agreementUniqueReference}/status-history", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "agreementUniqueReference", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.PayToController", "Method": "InitiatePayment", "RelativePath": "api/PayTo/payments", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "MonoovaPaymentIntegration.DTOs.PayToApi.PaymentInitiationRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.PayToController", "Method": "ListPaymentInitiations", "RelativePath": "api/PayTo/payments", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "agreementUniqueReference", "Type": "System.String", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.PayToController", "Method": "GetPaymentInitiation", "RelativePath": "api/PayTo/payments/{initiationUniqueReference}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "initiationUniqueReference", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.PayToController", "Method": "CancelPaymentInitiation", "RelativePath": "api/PayTo/payments/{initiationUniqueReference}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "initiationUniqueReference", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.PayToController", "Method": "GetPaymentStatusHistory", "RelativePath": "api/PayTo/payments/{initiationUniqueReference}/status-history", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "initiationUniqueReference", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.StoredProcedureController", "Method": "GetAccountBalanceSummary", "RelativePath": "api/StoredProcedure/account-balance-summary", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.StoredProcedureController", "Method": "ArchiveOldTransactions", "RelativePath": "api/StoredProcedure/archive-transactions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "MonoovaPaymentIntegration.Controllers.ArchiveRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.StoredProcedureController", "Method": "ProcessBulkPaymentUpdates", "RelativePath": "api/StoredProcedure/bulk-payment-updates", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "MonoovaPaymentIntegration.Controllers.BulkPaymentUpdateRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.StoredProcedureController", "Method": "GetCardPaymentStats", "RelativePath": "api/StoredProcedure/card-payment-stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fromDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "toDate", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.StoredProcedureController", "Method": "GetPaymentSummary", "RelativePath": "api/StoredProcedure/payment-summary/{accountId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "accountId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MonoovaPaymentIntegration.Controllers.StoredProcedureController", "Method": "GetTransactionsByDateRange", "RelativePath": "api/StoredProcedure/transactions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "endDate", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}]