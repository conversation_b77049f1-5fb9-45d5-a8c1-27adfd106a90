using MonoovaPaymentIntegration.DTOs.PayToApi;

namespace MonoovaPaymentIntegration.Interfaces
{
    public interface IMonoovaPayToService
    {
        // Payment Agreement Management
        Task<PaymentAgreementResponse> CreatePaymentAgreementAsync(PaymentAgreementRequest request);
        Task<PaymentAgreementResponse> GetPaymentAgreementAsync(string agreementUniqueReference);
        Task<PaymentAgreementResponse> UpdatePaymentAgreementAsync(string agreementUniqueReference, PaymentAgreementRequest request);
        Task<PaymentAgreementResponse> CancelPaymentAgreementAsync(string agreementUniqueReference);
        Task<object> ListPaymentAgreementsAsync(int pageNumber = 1, int pageSize = 50);

        // Payment Initiation
        Task<PaymentInitiationResponse> InitiatePaymentAsync(PaymentInitiationRequest request);
        Task<PaymentInitiationResponse> GetPaymentInitiationAsync(string initiationUniqueReference);
        Task<PaymentInitiationResponse> CancelPaymentInitiationAsync(string initiationUniqueReference);
        Task<object> ListPaymentInitiationsAsync(string? agreementUniqueReference = null, int pageNumber = 1, int pageSize = 50);

        // Agreement Amendments
        Task<object> CreateAgreementAmendmentAsync(string agreementUniqueReference, object amendmentRequest);
        Task<object> GetAgreementAmendmentAsync(string agreementUniqueReference, string amendmentId);
        Task<object> ListAgreementAmendmentsAsync(string agreementUniqueReference);

        // Reports and Status
        Task<object> GetAgreementStatusHistoryAsync(string agreementUniqueReference);
        Task<object> GetPaymentStatusHistoryAsync(string initiationUniqueReference);
    }
}
