using MonoovaPaymentIntegration.DTOs.PaymentsApi;

namespace MonoovaPaymentIntegration.Interfaces
{
    public interface IMonoovaPaymentsService
    {
        // Financial Transactions
        Task<TransactionExecuteResponse> ExecuteTransactionAsync(TransactionExecuteRequest request);
        Task<TransactionExecuteResponse> ValidateTransactionAsync(TransactionExecuteRequest request);
        Task<object> GetTransactionStatusAsync(string uniqueReference);
        Task<object> GetTransactionStatusByDateAsync(DateTime startDate, DateTime endDate);

        // BPAY Operations
        Task<BPAYValidateResponse> ValidateBPAYAsync(string billerCode, string? customerRef = null, decimal? amount = null);
        Task<object> GetBPAYBillerAsync(string billerCode);
        Task<object> SearchBPAYBillersAsync(string searchTerm, int skip = 0, int take = 50);
        Task<object> GetBPAYHistoryAsync(string accountNumber, int take = 50);
        Task<object> GetBPAYReceiptsAsync(object request);

        // mAccount Management
        Task<MAccountCreateResponse> CreateMAccountAsync(MAccountCreateRequest request);
        Task<object> GetMAccountAsync(string accountNumber);
        Task<object> GetMAccountFinancialsAsync(string accountNumber);
        Task<object> CloseMAccountAsync(string accountNumber);
        Task<object> ListMAccountsAsync();
        Task<object> GetMAccountTransactionsAsync(object request);

        // Verification
        Task<object> InitiateAccountVerificationAsync(object request);
        Task<object> ValidateAccountVerificationAsync(object request);

        // Reports
        Task<object> GetUnclearedFundsAsync(DateTime startDate, DateTime? endDate = null, int pageNumber = 1, int pageSize = 50);
    }
}
