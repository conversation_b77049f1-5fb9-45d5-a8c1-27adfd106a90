### MonoovaPayment - Stored Procedures API Testing
### Base URL - Update this to match your application's URL
@baseUrl = https://localhost:7000

### 1. Get Transactions by Date Range
GET {{baseUrl}}/api/StoredProcedure/transactions?startDate=2024-01-01&endDate=2024-12-31
Content-Type: application/json

###

### 2. Get Payment Summary by Account ID
GET {{baseUrl}}/api/StoredProcedure/payment-summary/1
Content-Type: application/json

###

### 3. Get Card Payment Statistics
GET {{baseUrl}}/api/StoredProcedure/card-payment-stats?fromDate=2024-01-01&toDate=2024-12-31
Content-Type: application/json

###

### 4. Get Account Balance Summary
GET {{baseUrl}}/api/StoredProcedure/account-balance-summary
Content-Type: application/json

###

### 5. Process Bulk Payment Updates
POST {{baseUrl}}/api/StoredProcedure/bulk-payment-updates
Content-Type: application/json

{
  "paymentIds": [1, 2, 3, 4, 5],
  "status": "Completed"
}

###

### 6. Archive Old Transactions
POST {{baseUrl}}/api/StoredProcedure/archive-transactions
Content-Type: application/json

{
  "cutoffDate": "2023-12-31T23:59:59Z"
}

###

### Test with different date formats
GET {{baseUrl}}/api/StoredProcedure/transactions?startDate=2024-01-01T00:00:00Z&endDate=2024-12-31T23:59:59Z
Content-Type: application/json

###

### Test with different account ID
GET {{baseUrl}}/api/StoredProcedure/payment-summary/2
Content-Type: application/json

###

### Test bulk update with different status
POST {{baseUrl}}/api/StoredProcedure/bulk-payment-updates
Content-Type: application/json

{
  "paymentIds": [6, 7, 8],
  "status": "Failed"
}

###

### Test archive with different cutoff date
POST {{baseUrl}}/api/StoredProcedure/archive-transactions
Content-Type: application/json

{
  "cutoffDate": "2024-06-30T23:59:59Z"
}
