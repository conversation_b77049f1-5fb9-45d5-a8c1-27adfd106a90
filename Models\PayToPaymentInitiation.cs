using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MonoovaPaymentIntegration.Models
{
    public class PayToPaymentInitiation : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string InitiationUniqueReference { get; set; } = string.Empty;
        
        [Required]
        public int PayToAgreementId { get; set; }
        
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }
        
        [Required]
        [MaxLength(3)]
        public string Currency { get; set; } = "AUD";
        
        [Required]
        [MaxLength(50)]
        public string Status { get; set; } = string.Empty; // Pending, Complete, Failed, etc.
        
        [MaxLength(500)]
        public string? Description { get; set; }
        
        public DateTime? ProcessedAt { get; set; }
        
        [MaxLength(1000)]
        public string? ErrorMessage { get; set; }
        
        [MaxLength(100)]
        public string? MonoovaInitiationId { get; set; }
        
        [MaxLength(100)]
        public string? ReasonCode { get; set; }
        
        [MaxLength(500)]
        public string? ReasonDescription { get; set; }
        
        // Navigation property
        [ForeignKey("PayToAgreementId")]
        public virtual PayToAgreement PayToAgreement { get; set; } = null!;
    }
}
