using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MonoovaPaymentIntegration.Models
{
    public class BPAYTransaction : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string UniqueReference { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(10)]
        public string BillerCode { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(20)]
        public string CustomerReferenceNumber { get; set; } = string.Empty;
        
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }
        
        [Required]
        [MaxLength(3)]
        public string Currency { get; set; } = "AUD";
        
        [Required]
        [MaxLength(50)]
        public string Status { get; set; } = string.Empty; // Pending, Complete, Failed
        
        [MaxLength(500)]
        public string? Description { get; set; }
        
        [Required]
        [MaxLength(16)]
        public string SourceWalletNumber { get; set; } = string.Empty;
        
        [MaxLength(200)]
        public string? BillerName { get; set; }
        
        [MaxLength(500)]
        public string? BillerDescription { get; set; }
        
        public DateTime? ProcessedAt { get; set; }
        
        [MaxLength(1000)]
        public string? ErrorMessage { get; set; }
        
        [MaxLength(100)]
        public string? MonoovaTransactionId { get; set; }
        
        [MaxLength(100)]
        public string? ReceiptNumber { get; set; }
        
        public bool IsValidated { get; set; } = false;
        
        public DateTime? ValidatedAt { get; set; }
    }
}
