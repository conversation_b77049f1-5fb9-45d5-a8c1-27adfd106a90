using System.ComponentModel.DataAnnotations;

namespace MonoovaPaymentIntegration.DTOs.PayToApi
{
    public class PaymentInitiationRequest
    {
        [Required]
        public string InitiationUniqueReference { get; set; } = string.Empty;
        
        [Required]
        public string AgreementUniqueReference { get; set; } = string.Empty;
        
        [Required]
        public decimal Amount { get; set; }
        
        [Required]
        public string Currency { get; set; } = "AUD";
        
        public string? Description { get; set; }
        
        public DateTime? RequestedExecutionDate { get; set; }
    }

    public class PaymentInitiationResponse
    {
        public string? Status { get; set; }
        public string? StatusDescription { get; set; }
        public string? InitiationUniqueReference { get; set; }
        public string? InitiationId { get; set; }
        public DateTime? CreatedDateTime { get; set; }
        public DateTime? ProcessedDateTime { get; set; }
        public string? ReasonCode { get; set; }
        public string? ReasonDescription { get; set; }
        public decimal? Amount { get; set; }
        public string? Currency { get; set; }
    }
}
