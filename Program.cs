using Microsoft.EntityFrameworkCore;
using MonoovaPaymentIntegration.Configuration;
using MonoovaPaymentIntegration.Data;
using MonoovaPaymentIntegration.Interfaces;
using MonoovaPaymentIntegration.Services;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddOpenApi();

// Configure Entity Framework
builder.Services.AddDbContext<MonoovaDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

// Configure settings
builder.Services.Configure<MonoovaSettings>(
    builder.Configuration.GetSection(MonoovaSettings.SectionName));

// Register services
builder.Services.AddScoped<IMonoovaPaymentsService, MonoovaPaymentsService>();
builder.Services.AddScoped<IMonoovaPayToService, MonoovaPayToService>();
builder.Services.AddScoped<IStoredProcedureService, StoredProcedureService>();

// Configure HTTP clients
builder.Services.AddHttpClient();

// Configure AutoMapper
builder.Services.AddAutoMapper(typeof(Program));

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
    app.UseDeveloperExceptionPage();
}

app.UseHttpsRedirection();
app.UseRouting();

app.MapControllers();

// Ensure database is created
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<MonoovaDbContext>();
    try
    {
        context.Database.EnsureCreated();
        Log.Information("Database connection verified successfully");
    }
    catch (Exception ex)
    {
        Log.Error(ex, "Failed to connect to database");
    }
}

app.Run();
