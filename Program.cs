using Microsoft.EntityFrameworkCore;
using MonoovaPaymentIntegration.Configuration;
using MonoovaPaymentIntegration.Data;
using MonoovaPaymentIntegration.Interfaces;
using MonoovaPaymentIntegration.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddOpenApi();

// Configure Entity Framework
builder.Services.AddDbContext<MonoovaDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

// Configure settings
builder.Services.Configure<MonoovaSettings>(
    builder.Configuration.GetSection(MonoovaSettings.SectionName));

// Register services
builder.Services.AddScoped<IMonoovaPaymentsService, MonoovaPaymentsService>();
builder.Services.AddScoped<IMonoovaPayToService, MonoovaPayToService>();
builder.Services.AddScoped<IStoredProcedureService, StoredProcedureService>();

// Configure HTTP clients
builder.Services.AddHttpClient();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
    app.UseDeveloperExceptionPage();
}

app.UseHttpsRedirection();
app.UseRouting();

app.MapControllers();

app.Run();
