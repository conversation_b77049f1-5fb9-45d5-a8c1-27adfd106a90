using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using MonoovaPaymentIntegration.Data;
using MonoovaPaymentIntegration.Models;
using MonoovaPaymentIntegration.Interfaces;

namespace MonoovaPaymentIntegration.Services
{
    public class StoredProcedureService : IStoredProcedureService
    {
        private readonly MonoovaDbContext _context;

        public StoredProcedureService(MonoovaDbContext context)
        {
            _context = context;
        }

        // Example: Get transactions by date range using stored procedure
        public async Task<List<Transaction>> GetTransactionsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            var startParam = new SqlParameter("@StartDate", startDate);
            var endParam = new SqlParameter("@EndDate", endDate);

            return await _context.ExecuteStoredProcedureAsync<Transaction>(
                "sp_GetTransactionsByDateRange", 
                startParam, 
                endParam
            );
        }

        // Example: Get payment summary using stored procedure
        public async Task<List<PaymentSummary>> GetPaymentSummaryAsync(int accountId)
        {
            var accountParam = new SqlParameter("@AccountId", accountId);

            return await _context.ExecuteStoredProcedureAsync<PaymentSummary>(
                "sp_GetPaymentSummary", 
                accountParam
            );
        }

        // Example: Process bulk payment updates using stored procedure
        public async Task<int> ProcessBulkPaymentUpdatesAsync(string paymentIds, string status)
        {
            var idsParam = new SqlParameter("@PaymentIds", paymentIds);
            var statusParam = new SqlParameter("@Status", status);

            return await _context.ExecuteStoredProcedureNonQueryAsync(
                "sp_ProcessBulkPaymentUpdates", 
                idsParam, 
                statusParam
            );
        }

        // Example: Get card payment statistics using stored procedure
        public async Task<List<CardPaymentStats>> GetCardPaymentStatsAsync(DateTime fromDate, DateTime toDate)
        {
            var fromParam = new SqlParameter("@FromDate", fromDate);
            var toParam = new SqlParameter("@ToDate", toDate);

            return await _context.ExecuteStoredProcedureAsync<CardPaymentStats>(
                "sp_GetCardPaymentStats", 
                fromParam, 
                toParam
            );
        }

        // Example: Archive old transactions using stored procedure
        public async Task<int> ArchiveOldTransactionsAsync(DateTime cutoffDate)
        {
            var cutoffParam = new SqlParameter("@CutoffDate", cutoffDate);

            return await _context.ExecuteStoredProcedureNonQueryAsync(
                "sp_ArchiveOldTransactions", 
                cutoffParam
            );
        }

        // Example: Get account balance summary using stored procedure
        public async Task<List<AccountBalanceSummary>> GetAccountBalanceSummaryAsync()
        {
            return await _context.ExecuteStoredProcedureAsync<AccountBalanceSummary>(
                "sp_GetAccountBalanceSummary"
            );
        }

        // Example: Execute custom SQL with parameters
        public async Task<int> ExecuteCustomSqlAsync(string sql, params SqlParameter[] parameters)
        {
            return await _context.ExecuteRawSqlAsync(sql, parameters);
        }
    }

    // DTOs for stored procedure results
    public class PaymentSummary
    {
        public int AccountId { get; set; }
        public string AccountNumber { get; set; } = string.Empty;
        public decimal TotalAmount { get; set; }
        public int TransactionCount { get; set; }
        public DateTime LastTransactionDate { get; set; }
    }

    public class CardPaymentStats
    {
        public string PaymentMethod { get; set; } = string.Empty;
        public decimal TotalAmount { get; set; }
        public int SuccessfulCount { get; set; }
        public int FailedCount { get; set; }
        public decimal AverageAmount { get; set; }
    }

    public class AccountBalanceSummary
    {
        public int AccountId { get; set; }
        public string AccountNumber { get; set; } = string.Empty;
        public decimal CurrentBalance { get; set; }
        public decimal AvailableBalance { get; set; }
        public decimal PendingAmount { get; set; }
    }
}
