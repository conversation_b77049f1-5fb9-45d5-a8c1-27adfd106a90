using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MonoovaPaymentIntegration.Models
{
    public class CardPayment : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string TransactionUniqueReference { get; set; } = string.Empty;
        
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }
        
        [Required]
        [MaxLength(3)]
        public string Currency { get; set; } = "AUD";
        
        [Required]
        [MaxLength(50)]
        public string Status { get; set; } = string.Empty; // Pending, Complete, Failed, Refunded, etc.
        
        [MaxLength(500)]
        public string? Description { get; set; }
        
        [Required]
        [MaxLength(50)]
        public string CardType { get; set; } = string.Empty; // Visa, MasterCard, Amex
        
        [MaxLength(4)]
        public string? CardLastFourDigits { get; set; }
        
        [MaxLength(100)]
        public string? CustomerFirstName { get; set; }
        
        [MaxLength(100)]
        public string? CustomerLastName { get; set; }
        
        [MaxLength(200)]
        public string? CustomerEmail { get; set; }
        
        [MaxLength(500)]
        public string? BillingAddress { get; set; }
        
        [MaxLength(100)]
        public string? BillingCity { get; set; }
        
        [MaxLength(50)]
        public string? BillingState { get; set; }
        
        [MaxLength(20)]
        public string? BillingPostalCode { get; set; }
        
        [MaxLength(3)]
        public string? BillingCountryCode { get; set; }
        
        public DateTime? ProcessedAt { get; set; }
        
        [MaxLength(1000)]
        public string? ErrorMessage { get; set; }
        
        [MaxLength(100)]
        public string? GatewayTransactionId { get; set; }
        
        [MaxLength(100)]
        public string? AuthorizationCode { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? RefundedAmount { get; set; }
        
        public DateTime? RefundedAt { get; set; }
        
        [MaxLength(500)]
        public string? RefundReason { get; set; }
        
        // Navigation property for refunds
        public virtual ICollection<CardPaymentRefund> Refunds { get; set; } = new List<CardPaymentRefund>();
    }
}
