using Microsoft.Extensions.Options;
using MonoovaPaymentIntegration.Configuration;
using MonoovaPaymentIntegration.DTOs.PaymentsApi;
using MonoovaPaymentIntegration.Interfaces;
using Newtonsoft.Json;
using System.Text;

namespace MonoovaPaymentIntegration.Services
{
    public class MonoovaPaymentsService : IMonoovaPaymentsService
    {
        private readonly HttpClient _httpClient;
        private readonly PaymentsApiSettings _settings;
        private readonly ILogger<MonoovaPaymentsService> _logger;

        public MonoovaPaymentsService(
            HttpClient httpClient,
            IOptions<MonoovaSettings> settings,
            ILogger<MonoovaPaymentsService> logger)
        {
            _httpClient = httpClient;
            _settings = settings.Value.PaymentsApi;
            _logger = logger;

            // Configure HttpClient
            _httpClient.BaseAddress = new Uri(_settings.BaseUrl);
            _httpClient.Timeout = TimeSpan.FromSeconds(_settings.TimeoutSeconds);
            
            // Set up Basic Authentication with API Key
            var authValue = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{_settings.ApiKey}:"));
            _httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", authValue);
        }

        public async Task<TransactionExecuteResponse> ExecuteTransactionAsync(TransactionExecuteRequest request)
        {
            try
            {
                _logger.LogInformation("Executing transaction with reference: {Reference}", request.CallerUniqueReference);
                
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync("/financial/v2/transaction/execute", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<TransactionExecuteResponse>(responseContent);
                    _logger.LogInformation("Transaction executed successfully: {Reference}", request.CallerUniqueReference);
                    return result ?? new TransactionExecuteResponse();
                }
                else
                {
                    _logger.LogError("Transaction execution failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Transaction execution failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing transaction: {Reference}", request.CallerUniqueReference);
                throw;
            }
        }

        public async Task<TransactionExecuteResponse> ValidateTransactionAsync(TransactionExecuteRequest request)
        {
            try
            {
                _logger.LogInformation("Validating transaction with reference: {Reference}", request.CallerUniqueReference);
                
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync("/financial/v2/transaction/validate", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<TransactionExecuteResponse>(responseContent);
                    _logger.LogInformation("Transaction validated successfully: {Reference}", request.CallerUniqueReference);
                    return result ?? new TransactionExecuteResponse();
                }
                else
                {
                    _logger.LogError("Transaction validation failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Transaction validation failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating transaction: {Reference}", request.CallerUniqueReference);
                throw;
            }
        }

        public async Task<object> GetTransactionStatusAsync(string uniqueReference)
        {
            try
            {
                _logger.LogInformation("Getting transaction status for reference: {Reference}", uniqueReference);
                
                var response = await _httpClient.GetAsync($"/financial/v2/status/{uniqueReference}");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    _logger.LogInformation("Transaction status retrieved successfully: {Reference}", uniqueReference);
                    return result ?? new object();
                }
                else
                {
                    _logger.LogError("Get transaction status failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Get transaction status failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transaction status: {Reference}", uniqueReference);
                throw;
            }
        }

        public async Task<object> GetTransactionStatusByDateAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                _logger.LogInformation("Getting transaction status by date range: {StartDate} to {EndDate}", startDate, endDate);
                
                var startDateStr = startDate.ToString("yyyy-MM-dd");
                var endDateStr = endDate.ToString("yyyy-MM-dd");
                
                var response = await _httpClient.GetAsync($"/financial/v2/status/{startDateStr}/{endDateStr}");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    _logger.LogInformation("Transaction status by date retrieved successfully");
                    return result ?? new object();
                }
                else
                {
                    _logger.LogError("Get transaction status by date failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Get transaction status by date failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transaction status by date");
                throw;
            }
        }

        public async Task<BPAYValidateResponse> ValidateBPAYAsync(string billerCode, string? customerRef = null, decimal? amount = null)
        {
            try
            {
                _logger.LogInformation("Validating BPAY: {BillerCode}", billerCode);
                
                var queryParams = new List<string>();
                if (!string.IsNullOrEmpty(customerRef))
                    queryParams.Add($"custRef={Uri.EscapeDataString(customerRef)}");
                if (amount.HasValue)
                    queryParams.Add($"amount={amount.Value}");
                
                var queryString = queryParams.Count > 0 ? "?" + string.Join("&", queryParams) : "";
                
                var response = await _httpClient.GetAsync($"/bpay/v1/validate/{billerCode}{queryString}");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<BPAYValidateResponse>(responseContent);
                    _logger.LogInformation("BPAY validation completed: {BillerCode}", billerCode);
                    return result ?? new BPAYValidateResponse();
                }
                else
                {
                    _logger.LogError("BPAY validation failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"BPAY validation failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating BPAY: {BillerCode}", billerCode);
                throw;
            }
        }

        public async Task<object> GetBPAYBillerAsync(string billerCode)
        {
            try
            {
                _logger.LogInformation("Getting BPAY biller: {BillerCode}", billerCode);
                
                var response = await _httpClient.GetAsync($"/bpay/v1/biller/{billerCode}");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    _logger.LogInformation("BPAY biller retrieved successfully: {BillerCode}", billerCode);
                    return result ?? new object();
                }
                else
                {
                    _logger.LogError("Get BPAY biller failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Get BPAY biller failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting BPAY biller: {BillerCode}", billerCode);
                throw;
            }
        }

        public async Task<object> SearchBPAYBillersAsync(string searchTerm, int skip = 0, int take = 50)
        {
            try
            {
                _logger.LogInformation("Searching BPAY billers: {SearchTerm}", searchTerm);
                
                var queryParams = new List<string>
                {
                    $"search={Uri.EscapeDataString(searchTerm)}",
                    $"skip={skip}",
                    $"take={take}"
                };
                
                var queryString = "?" + string.Join("&", queryParams);
                
                var response = await _httpClient.GetAsync($"/bpay/v1/billers{queryString}");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    _logger.LogInformation("BPAY billers search completed: {SearchTerm}", searchTerm);
                    return result ?? new object();
                }
                else
                {
                    _logger.LogError("Search BPAY billers failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Search BPAY billers failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching BPAY billers: {SearchTerm}", searchTerm);
                throw;
            }
        }

        public async Task<object> GetBPAYHistoryAsync(string accountNumber, int take = 50)
        {
            try
            {
                _logger.LogInformation("Getting BPAY history for account: {AccountNumber}", accountNumber);
                
                var response = await _httpClient.GetAsync($"/bpay/v1/history/{accountNumber}?take={take}");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    _logger.LogInformation("BPAY history retrieved successfully: {AccountNumber}", accountNumber);
                    return result ?? new object();
                }
                else
                {
                    _logger.LogError("Get BPAY history failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Get BPAY history failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting BPAY history: {AccountNumber}", accountNumber);
                throw;
            }
        }

        public async Task<object> GetBPAYReceiptsAsync(object request)
        {
            try
            {
                _logger.LogInformation("Getting BPAY receipts");
                
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync("/bpay/v1/receipts", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    _logger.LogInformation("BPAY receipts retrieved successfully");
                    return result ?? new object();
                }
                else
                {
                    _logger.LogError("Get BPAY receipts failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Get BPAY receipts failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting BPAY receipts");
                throw;
            }
        }

        public async Task<MAccountCreateResponse> CreateMAccountAsync(MAccountCreateRequest request)
        {
            try
            {
                _logger.LogInformation("Creating mAccount: {AccountName}", request.AccountName);
                
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync("/mAccount/v1/create", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<MAccountCreateResponse>(responseContent);
                    _logger.LogInformation("mAccount created successfully: {AccountName}", request.AccountName);
                    return result ?? new MAccountCreateResponse();
                }
                else
                {
                    _logger.LogError("Create mAccount failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Create mAccount failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating mAccount: {AccountName}", request.AccountName);
                throw;
            }
        }

        public async Task<object> GetMAccountAsync(string accountNumber)
        {
            try
            {
                _logger.LogInformation("Getting mAccount: {AccountNumber}", accountNumber);
                
                var response = await _httpClient.GetAsync($"/mAccount/v1/get/{accountNumber}");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    _logger.LogInformation("mAccount retrieved successfully: {AccountNumber}", accountNumber);
                    return result ?? new object();
                }
                else
                {
                    _logger.LogError("Get mAccount failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Get mAccount failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting mAccount: {AccountNumber}", accountNumber);
                throw;
            }
        }

        public async Task<object> GetMAccountFinancialsAsync(string accountNumber)
        {
            try
            {
                _logger.LogInformation("Getting mAccount financials: {AccountNumber}", accountNumber);
                
                var response = await _httpClient.GetAsync($"/mAccount/v1/financials/{accountNumber}");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    _logger.LogInformation("mAccount financials retrieved successfully: {AccountNumber}", accountNumber);
                    return result ?? new object();
                }
                else
                {
                    _logger.LogError("Get mAccount financials failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Get mAccount financials failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting mAccount financials: {AccountNumber}", accountNumber);
                throw;
            }
        }

        public async Task<object> CloseMAccountAsync(string accountNumber)
        {
            try
            {
                _logger.LogInformation("Closing mAccount: {AccountNumber}", accountNumber);
                
                var response = await _httpClient.GetAsync($"/mAccount/v1/close/{accountNumber}");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    _logger.LogInformation("mAccount closed successfully: {AccountNumber}", accountNumber);
                    return result ?? new object();
                }
                else
                {
                    _logger.LogError("Close mAccount failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Close mAccount failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error closing mAccount: {AccountNumber}", accountNumber);
                throw;
            }
        }

        public async Task<object> ListMAccountsAsync()
        {
            try
            {
                _logger.LogInformation("Listing mAccounts");
                
                var response = await _httpClient.GetAsync("/mAccount/v1/listAsIssuer");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    _logger.LogInformation("mAccounts listed successfully");
                    return result ?? new object();
                }
                else
                {
                    _logger.LogError("List mAccounts failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"List mAccounts failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing mAccounts");
                throw;
            }
        }

        public async Task<object> GetMAccountTransactionsAsync(object request)
        {
            try
            {
                _logger.LogInformation("Getting mAccount transactions");
                
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync("/mAccount/v1/transactions", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    _logger.LogInformation("mAccount transactions retrieved successfully");
                    return result ?? new object();
                }
                else
                {
                    _logger.LogError("Get mAccount transactions failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Get mAccount transactions failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting mAccount transactions");
                throw;
            }
        }

        public async Task<object> InitiateAccountVerificationAsync(object request)
        {
            try
            {
                _logger.LogInformation("Initiating account verification");
                
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync("/verify/v1/aba/initiate", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    _logger.LogInformation("Account verification initiated successfully");
                    return result ?? new object();
                }
                else
                {
                    _logger.LogError("Initiate account verification failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Initiate account verification failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initiating account verification");
                throw;
            }
        }

        public async Task<object> ValidateAccountVerificationAsync(object request)
        {
            try
            {
                _logger.LogInformation("Validating account verification");
                
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync("/verify/v1/aba/validate", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    _logger.LogInformation("Account verification validated successfully");
                    return result ?? new object();
                }
                else
                {
                    _logger.LogError("Validate account verification failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Validate account verification failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating account verification");
                throw;
            }
        }

        public async Task<object> GetUnclearedFundsAsync(DateTime startDate, DateTime? endDate = null, int pageNumber = 1, int pageSize = 50)
        {
            try
            {
                _logger.LogInformation("Getting uncleared funds for date: {StartDate}", startDate);

                var startDateStr = startDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                var queryParams = new List<string>
                {
                    $"pageNumber={pageNumber}",
                    $"pageSize={pageSize}"
                };

                if (endDate.HasValue)
                {
                    var endDateStr = endDate.Value.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                    queryParams.Add($"endDate={Uri.EscapeDataString(endDateStr)}");
                }

                var queryString = "?" + string.Join("&", queryParams);

                var response = await _httpClient.GetAsync($"/reports/v1/unclearedFunds/{Uri.EscapeDataString(startDateStr)}{queryString}");
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    _logger.LogInformation("Uncleared funds retrieved successfully");
                    return result ?? new object();
                }
                else
                {
                    _logger.LogError("Get uncleared funds failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Get uncleared funds failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting uncleared funds");
                throw;
            }
        }
    }
}
