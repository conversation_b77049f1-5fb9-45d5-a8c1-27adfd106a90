namespace MonoovaPaymentIntegration.DTOs.PaymentsApi
{
    public class BPAYValidateResponse
    {
        public string? DurationMs { get; set; }
        public string? Status { get; set; }
        public string? StatusDescription { get; set; }
        public string? BillerCode { get; set; }
        public string? BillerLongName { get; set; }
        public string? BillerShortName { get; set; }
        public string? CustomerReferenceNumber { get; set; }
        public decimal? Amount { get; set; }
        public bool? IsValidCombination { get; set; }
        public string? ValidationMessage { get; set; }
    }
}
