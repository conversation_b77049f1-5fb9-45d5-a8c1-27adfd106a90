using Microsoft.Extensions.Options;
using MonoovaPaymentIntegration.Configuration;
using MonoovaPaymentIntegration.DTOs.PayToApi;
using MonoovaPaymentIntegration.Interfaces;
using Newtonsoft.Json;
using System.Text;

namespace MonoovaPaymentIntegration.Services
{
    public class MonoovaPayToService : IMonoovaPayToService
    {
        private readonly HttpClient _httpClient;
        private readonly PayToApiSettings _settings;
        private readonly ILogger<MonoovaPayToService> _logger;

        public MonoovaPayToService(
            HttpClient httpClient,
            IOptions<MonoovaSettings> settings,
            ILogger<MonoovaPayToService> logger)
        {
            _httpClient = httpClient;
            _settings = settings.Value.PayToApi;
            _logger = logger;

            // Configure HttpClient
            _httpClient.BaseAddress = new Uri(_settings.BaseUrl);
            _httpClient.Timeout = TimeSpan.FromSeconds(_settings.TimeoutSeconds);
            
            // Set up Basic Authentication with API Key
            var authValue = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{_settings.ApiKey}:"));
            _httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", authValue);
            
            // Add Client ID header if provided
            if (!string.IsNullOrEmpty(_settings.ClientId))
            {
                _httpClient.DefaultRequestHeaders.Add("X-Client-Id", _settings.ClientId);
            }
        }

        public async Task<PaymentAgreementResponse> CreatePaymentAgreementAsync(PaymentAgreementRequest request)
        {
            try
            {
                _logger.LogInformation("Creating PayTo agreement: {Reference}", request.AgreementUniqueReference);
                
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync("/payto/v1/agreements", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<PaymentAgreementResponse>(responseContent);
                    _logger.LogInformation("PayTo agreement created successfully: {Reference}", request.AgreementUniqueReference);
                    return result ?? new PaymentAgreementResponse();
                }
                else
                {
                    _logger.LogError("Create PayTo agreement failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Create PayTo agreement failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating PayTo agreement: {Reference}", request.AgreementUniqueReference);
                throw;
            }
        }

        public async Task<PaymentAgreementResponse> GetPaymentAgreementAsync(string agreementUniqueReference)
        {
            try
            {
                _logger.LogInformation("Getting PayTo agreement: {Reference}", agreementUniqueReference);
                
                var response = await _httpClient.GetAsync($"/payto/v1/agreements/{agreementUniqueReference}");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<PaymentAgreementResponse>(responseContent);
                    _logger.LogInformation("PayTo agreement retrieved successfully: {Reference}", agreementUniqueReference);
                    return result ?? new PaymentAgreementResponse();
                }
                else
                {
                    _logger.LogError("Get PayTo agreement failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Get PayTo agreement failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting PayTo agreement: {Reference}", agreementUniqueReference);
                throw;
            }
        }

        public async Task<PaymentAgreementResponse> UpdatePaymentAgreementAsync(string agreementUniqueReference, PaymentAgreementRequest request)
        {
            try
            {
                _logger.LogInformation("Updating PayTo agreement: {Reference}", agreementUniqueReference);
                
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PutAsync($"/payto/v1/agreements/{agreementUniqueReference}", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<PaymentAgreementResponse>(responseContent);
                    _logger.LogInformation("PayTo agreement updated successfully: {Reference}", agreementUniqueReference);
                    return result ?? new PaymentAgreementResponse();
                }
                else
                {
                    _logger.LogError("Update PayTo agreement failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Update PayTo agreement failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating PayTo agreement: {Reference}", agreementUniqueReference);
                throw;
            }
        }

        public async Task<PaymentAgreementResponse> CancelPaymentAgreementAsync(string agreementUniqueReference)
        {
            try
            {
                _logger.LogInformation("Cancelling PayTo agreement: {Reference}", agreementUniqueReference);
                
                var response = await _httpClient.DeleteAsync($"/payto/v1/agreements/{agreementUniqueReference}");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<PaymentAgreementResponse>(responseContent);
                    _logger.LogInformation("PayTo agreement cancelled successfully: {Reference}", agreementUniqueReference);
                    return result ?? new PaymentAgreementResponse();
                }
                else
                {
                    _logger.LogError("Cancel PayTo agreement failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Cancel PayTo agreement failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling PayTo agreement: {Reference}", agreementUniqueReference);
                throw;
            }
        }

        public async Task<object> ListPaymentAgreementsAsync(int pageNumber = 1, int pageSize = 50)
        {
            try
            {
                _logger.LogInformation("Listing PayTo agreements");
                
                var queryParams = new List<string>
                {
                    $"pageNumber={pageNumber}",
                    $"pageSize={pageSize}"
                };
                
                var queryString = "?" + string.Join("&", queryParams);
                
                var response = await _httpClient.GetAsync($"/payto/v1/agreements{queryString}");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    _logger.LogInformation("PayTo agreements listed successfully");
                    return result ?? new object();
                }
                else
                {
                    _logger.LogError("List PayTo agreements failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"List PayTo agreements failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing PayTo agreements");
                throw;
            }
        }

        public async Task<PaymentInitiationResponse> InitiatePaymentAsync(PaymentInitiationRequest request)
        {
            try
            {
                _logger.LogInformation("Initiating PayTo payment: {Reference}", request.InitiationUniqueReference);
                
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync("/payto/v1/payments", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<PaymentInitiationResponse>(responseContent);
                    _logger.LogInformation("PayTo payment initiated successfully: {Reference}", request.InitiationUniqueReference);
                    return result ?? new PaymentInitiationResponse();
                }
                else
                {
                    _logger.LogError("Initiate PayTo payment failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Initiate PayTo payment failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initiating PayTo payment: {Reference}", request.InitiationUniqueReference);
                throw;
            }
        }

        public async Task<PaymentInitiationResponse> GetPaymentInitiationAsync(string initiationUniqueReference)
        {
            try
            {
                _logger.LogInformation("Getting PayTo payment initiation: {Reference}", initiationUniqueReference);
                
                var response = await _httpClient.GetAsync($"/payto/v1/payments/{initiationUniqueReference}");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<PaymentInitiationResponse>(responseContent);
                    _logger.LogInformation("PayTo payment initiation retrieved successfully: {Reference}", initiationUniqueReference);
                    return result ?? new PaymentInitiationResponse();
                }
                else
                {
                    _logger.LogError("Get PayTo payment initiation failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Get PayTo payment initiation failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting PayTo payment initiation: {Reference}", initiationUniqueReference);
                throw;
            }
        }

        public async Task<PaymentInitiationResponse> CancelPaymentInitiationAsync(string initiationUniqueReference)
        {
            try
            {
                _logger.LogInformation("Cancelling PayTo payment initiation: {Reference}", initiationUniqueReference);
                
                var response = await _httpClient.DeleteAsync($"/payto/v1/payments/{initiationUniqueReference}");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<PaymentInitiationResponse>(responseContent);
                    _logger.LogInformation("PayTo payment initiation cancelled successfully: {Reference}", initiationUniqueReference);
                    return result ?? new PaymentInitiationResponse();
                }
                else
                {
                    _logger.LogError("Cancel PayTo payment initiation failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Cancel PayTo payment initiation failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling PayTo payment initiation: {Reference}", initiationUniqueReference);
                throw;
            }
        }

        public async Task<object> ListPaymentInitiationsAsync(string? agreementUniqueReference = null, int pageNumber = 1, int pageSize = 50)
        {
            try
            {
                _logger.LogInformation("Listing PayTo payment initiations");
                
                var queryParams = new List<string>
                {
                    $"pageNumber={pageNumber}",
                    $"pageSize={pageSize}"
                };
                
                if (!string.IsNullOrEmpty(agreementUniqueReference))
                {
                    queryParams.Add($"agreementUniqueReference={Uri.EscapeDataString(agreementUniqueReference)}");
                }
                
                var queryString = "?" + string.Join("&", queryParams);
                
                var response = await _httpClient.GetAsync($"/payto/v1/payments{queryString}");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    _logger.LogInformation("PayTo payment initiations listed successfully");
                    return result ?? new object();
                }
                else
                {
                    _logger.LogError("List PayTo payment initiations failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"List PayTo payment initiations failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing PayTo payment initiations");
                throw;
            }
        }

        public async Task<object> CreateAgreementAmendmentAsync(string agreementUniqueReference, object amendmentRequest)
        {
            try
            {
                _logger.LogInformation("Creating PayTo agreement amendment: {Reference}", agreementUniqueReference);
                
                var json = JsonConvert.SerializeObject(amendmentRequest);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync($"/payto/v1/agreements/{agreementUniqueReference}/amendments", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    _logger.LogInformation("PayTo agreement amendment created successfully: {Reference}", agreementUniqueReference);
                    return result ?? new object();
                }
                else
                {
                    _logger.LogError("Create PayTo agreement amendment failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Create PayTo agreement amendment failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating PayTo agreement amendment: {Reference}", agreementUniqueReference);
                throw;
            }
        }

        public async Task<object> GetAgreementAmendmentAsync(string agreementUniqueReference, string amendmentId)
        {
            try
            {
                _logger.LogInformation("Getting PayTo agreement amendment: {Reference} - {AmendmentId}", agreementUniqueReference, amendmentId);
                
                var response = await _httpClient.GetAsync($"/payto/v1/agreements/{agreementUniqueReference}/amendments/{amendmentId}");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    _logger.LogInformation("PayTo agreement amendment retrieved successfully: {Reference} - {AmendmentId}", agreementUniqueReference, amendmentId);
                    return result ?? new object();
                }
                else
                {
                    _logger.LogError("Get PayTo agreement amendment failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Get PayTo agreement amendment failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting PayTo agreement amendment: {Reference} - {AmendmentId}", agreementUniqueReference, amendmentId);
                throw;
            }
        }

        public async Task<object> ListAgreementAmendmentsAsync(string agreementUniqueReference)
        {
            try
            {
                _logger.LogInformation("Listing PayTo agreement amendments: {Reference}", agreementUniqueReference);
                
                var response = await _httpClient.GetAsync($"/payto/v1/agreements/{agreementUniqueReference}/amendments");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    _logger.LogInformation("PayTo agreement amendments listed successfully: {Reference}", agreementUniqueReference);
                    return result ?? new object();
                }
                else
                {
                    _logger.LogError("List PayTo agreement amendments failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"List PayTo agreement amendments failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing PayTo agreement amendments: {Reference}", agreementUniqueReference);
                throw;
            }
        }

        public async Task<object> GetAgreementStatusHistoryAsync(string agreementUniqueReference)
        {
            try
            {
                _logger.LogInformation("Getting PayTo agreement status history: {Reference}", agreementUniqueReference);
                
                var response = await _httpClient.GetAsync($"/payto/v1/agreements/{agreementUniqueReference}/status-history");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    _logger.LogInformation("PayTo agreement status history retrieved successfully: {Reference}", agreementUniqueReference);
                    return result ?? new object();
                }
                else
                {
                    _logger.LogError("Get PayTo agreement status history failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Get PayTo agreement status history failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting PayTo agreement status history: {Reference}", agreementUniqueReference);
                throw;
            }
        }

        public async Task<object> GetPaymentStatusHistoryAsync(string initiationUniqueReference)
        {
            try
            {
                _logger.LogInformation("Getting PayTo payment status history: {Reference}", initiationUniqueReference);
                
                var response = await _httpClient.GetAsync($"/payto/v1/payments/{initiationUniqueReference}/status-history");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject(responseContent);
                    _logger.LogInformation("PayTo payment status history retrieved successfully: {Reference}", initiationUniqueReference);
                    return result ?? new object();
                }
                else
                {
                    _logger.LogError("Get PayTo payment status history failed: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    throw new HttpRequestException($"Get PayTo payment status history failed: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting PayTo payment status history: {Reference}", initiationUniqueReference);
                throw;
            }
        }
    }
}
