namespace MonoovaPaymentIntegration.DTOs.PaymentsApi
{
    public class TransactionExecuteResponse
    {
        public string? DurationMs { get; set; }
        public string? Status { get; set; }
        public string? StatusDescription { get; set; }
        public List<TransactionResult>? Transactions { get; set; }
    }

    public class TransactionResult
    {
        public string? CallerUniqueReference { get; set; }
        public string? Status { get; set; }
        public string? StatusDescription { get; set; }
        public decimal? Amount { get; set; }
        public string? TransactionId { get; set; }
        public DateTime? CreatedDateTime { get; set; }
        public List<DisbursementResult>? Disbursements { get; set; }
    }

    public class DisbursementResult
    {
        public string? Status { get; set; }
        public string? StatusDescription { get; set; }
        public decimal? Amount { get; set; }
        public string? TransactionId { get; set; }
        public DateTime? CreatedDateTime { get; set; }
        public string? ToAccountNumber { get; set; }
        public string? ToBSB { get; set; }
        public string? ToAccountName { get; set; }
        public string? PayID { get; set; }
        public string? BillerCode { get; set; }
        public string? CustomerReferenceNumber { get; set; }
    }
}
