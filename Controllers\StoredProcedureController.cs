using Microsoft.AspNetCore.Mvc;
using MonoovaPaymentIntegration.Interfaces;
using MonoovaPaymentIntegration.Services;

namespace MonoovaPaymentIntegration.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class StoredProcedureController : ControllerBase
    {
        private readonly IStoredProcedureService _storedProcedureService;
        private readonly ILogger<StoredProcedureController> _logger;

        public StoredProcedureController(
            IStoredProcedureService storedProcedureService,
            ILogger<StoredProcedureController> logger)
        {
            _storedProcedureService = storedProcedureService;
            _logger = logger;
        }

        /// <summary>
        /// Get transactions by date range using stored procedure
        /// </summary>
        [HttpGet("transactions")]
        public async Task<IActionResult> GetTransactionsByDateRange(
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate)
        {
            try
            {
                var transactions = await _storedProcedureService.GetTransactionsByDateRangeAsync(startDate, endDate);
                return Ok(transactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transactions by date range");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get payment summary for an account using stored procedure
        /// </summary>
        [HttpGet("payment-summary/{accountId}")]
        public async Task<IActionResult> GetPaymentSummary(int accountId)
        {
            try
            {
                var summary = await _storedProcedureService.GetPaymentSummaryAsync(accountId);
                return Ok(summary);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payment summary for account {AccountId}", accountId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get card payment statistics using stored procedure
        /// </summary>
        [HttpGet("card-payment-stats")]
        public async Task<IActionResult> GetCardPaymentStats(
            [FromQuery] DateTime fromDate,
            [FromQuery] DateTime toDate)
        {
            try
            {
                var stats = await _storedProcedureService.GetCardPaymentStatsAsync(fromDate, toDate);
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting card payment stats");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get account balance summary using stored procedure
        /// </summary>
        [HttpGet("account-balance-summary")]
        public async Task<IActionResult> GetAccountBalanceSummary()
        {
            try
            {
                var summary = await _storedProcedureService.GetAccountBalanceSummaryAsync();
                return Ok(summary);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting account balance summary");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Process bulk payment updates using stored procedure
        /// </summary>
        [HttpPost("bulk-payment-updates")]
        public async Task<IActionResult> ProcessBulkPaymentUpdates(
            [FromBody] BulkPaymentUpdateRequest request)
        {
            try
            {
                var paymentIds = string.Join(",", request.PaymentIds);
                var result = await _storedProcedureService.ProcessBulkPaymentUpdatesAsync(paymentIds, request.Status);
                return Ok(new { UpdatedRecords = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing bulk payment updates");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Archive old transactions using stored procedure
        /// </summary>
        [HttpPost("archive-transactions")]
        public async Task<IActionResult> ArchiveOldTransactions([FromBody] ArchiveRequest request)
        {
            try
            {
                var result = await _storedProcedureService.ArchiveOldTransactionsAsync(request.CutoffDate);
                return Ok(new { ArchivedRecords = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error archiving old transactions");
                return StatusCode(500, "Internal server error");
            }
        }
    }

    // Request DTOs
    public class BulkPaymentUpdateRequest
    {
        public List<int> PaymentIds { get; set; } = new();
        public string Status { get; set; } = string.Empty;
    }

    public class ArchiveRequest
    {
        public DateTime CutoffDate { get; set; }
    }
}
