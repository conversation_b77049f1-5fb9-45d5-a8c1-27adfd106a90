using System.ComponentModel.DataAnnotations;

namespace MonoovaPaymentIntegration.DTOs.PaymentsApi
{
    public class TransactionExecuteRequest
    {
        [Required]
        public string CallerUniqueReference { get; set; } = string.Empty;
        
        [Required]
        public SourceDetails Source { get; set; } = new();
        
        [Required]
        public List<DisbursementDetails> Disbursements { get; set; } = new();
        
        public string? Description { get; set; }
    }

    public class SourceDetails
    {
        public MAccountDetails? MAccount { get; set; }
        public BankAccountDetails? BankAccount { get; set; }
        public string? Token { get; set; }
    }

    public class MAccountDetails
    {
        [Required]
        public string AccountNumber { get; set; } = string.Empty;
    }

    public class BankAccountDetails
    {
        [Required]
        public string BSB { get; set; } = string.Empty;
        
        [Required]
        public string AccountNumber { get; set; } = string.Empty;
        
        [Required]
        public string AccountName { get; set; } = string.Empty;
    }

    public class DisbursementDetails
    {
        [Required]
        public decimal Amount { get; set; }
        
        public MAccountDetails? MAccount { get; set; }
        public BankAccountDetails? BankAccount { get; set; }
        public BPAYDetails? BPAY { get; set; }
        public NPPDetails? NPP { get; set; }
        public string? Token { get; set; }
        public string? Description { get; set; }
    }

    public class BPAYDetails
    {
        [Required]
        public string BillerCode { get; set; } = string.Empty;
        
        [Required]
        public string CustomerReferenceNumber { get; set; } = string.Empty;
        
        [Required]
        public MWalletDetails MWallet { get; set; } = new();
    }

    public class MWalletDetails
    {
        [Required]
        public string AccountNumber { get; set; } = string.Empty;
    }

    public class NPPDetails
    {
        public string? PayID { get; set; }
        public BankAccountDetails? BankAccount { get; set; }
        public string? RemitterName { get; set; }
        public string? LodgementReference { get; set; }
    }
}
