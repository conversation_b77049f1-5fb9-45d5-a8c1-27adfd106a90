using Microsoft.EntityFrameworkCore;
using MonoovaPaymentIntegration.Models;

namespace MonoovaPaymentIntegration.Data
{
    public class MonoovaDbContext : DbContext
    {
        public MonoovaDbContext(DbContextOptions<MonoovaDbContext> options) : base(options)
        {
        }

        // DbSets for all entities
        public DbSet<Transaction> Transactions { get; set; }
        public DbSet<PayToAgreement> PayToAgreements { get; set; }
        public DbSet<PayToPaymentInitiation> PayToPaymentInitiations { get; set; }
        public DbSet<CardPayment> CardPayments { get; set; }
        public DbSet<CardPaymentRefund> CardPaymentRefunds { get; set; }
        public DbSet<MAccount> MAccounts { get; set; }
        public DbSet<BPAYTransaction> BPAYTransactions { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure entity relationships and constraints
            
            // PayToAgreement -> PayToPaymentInitiation (One-to-Many)
            modelBuilder.Entity<PayToPaymentInitiation>()
                .HasOne(p => p.PayToAgreement)
                .WithMany(a => a.PaymentInitiations)
                .HasForeignKey(p => p.PayToAgreementId)
                .OnDelete(DeleteBehavior.Cascade);

            // CardPayment -> CardPaymentRefund (One-to-Many)
            modelBuilder.Entity<CardPaymentRefund>()
                .HasOne(r => r.CardPayment)
                .WithMany(p => p.Refunds)
                .HasForeignKey(r => r.CardPaymentId)
                .OnDelete(DeleteBehavior.Cascade);

            // Configure indexes for better performance
            modelBuilder.Entity<Transaction>()
                .HasIndex(t => t.UniqueReference)
                .IsUnique();

            modelBuilder.Entity<PayToAgreement>()
                .HasIndex(a => a.AgreementUniqueReference)
                .IsUnique();

            modelBuilder.Entity<PayToPaymentInitiation>()
                .HasIndex(i => i.InitiationUniqueReference)
                .IsUnique();

            modelBuilder.Entity<CardPayment>()
                .HasIndex(c => c.TransactionUniqueReference)
                .IsUnique();

            modelBuilder.Entity<CardPaymentRefund>()
                .HasIndex(r => r.RefundUniqueReference)
                .IsUnique();

            modelBuilder.Entity<MAccount>()
                .HasIndex(m => m.AccountNumber)
                .IsUnique();

            modelBuilder.Entity<BPAYTransaction>()
                .HasIndex(b => b.UniqueReference)
                .IsUnique();

            // Configure decimal precision
            modelBuilder.Entity<Transaction>()
                .Property(t => t.Amount)
                .HasPrecision(18, 2);

            modelBuilder.Entity<PayToPaymentInitiation>()
                .Property(p => p.Amount)
                .HasPrecision(18, 2);

            modelBuilder.Entity<PayToAgreement>()
                .Property(a => a.Amount)
                .HasPrecision(18, 2);

            modelBuilder.Entity<PayToAgreement>()
                .Property(a => a.MaxAmount)
                .HasPrecision(18, 2);

            modelBuilder.Entity<CardPayment>()
                .Property(c => c.Amount)
                .HasPrecision(18, 2);

            modelBuilder.Entity<CardPayment>()
                .Property(c => c.RefundedAmount)
                .HasPrecision(18, 2);

            modelBuilder.Entity<CardPaymentRefund>()
                .Property(r => r.RefundAmount)
                .HasPrecision(18, 2);

            modelBuilder.Entity<MAccount>()
                .Property(m => m.Balance)
                .HasPrecision(18, 2);

            modelBuilder.Entity<MAccount>()
                .Property(m => m.AvailableBalance)
                .HasPrecision(18, 2);

            modelBuilder.Entity<MAccount>()
                .Property(m => m.UnclearedFunds)
                .HasPrecision(18, 2);

            modelBuilder.Entity<BPAYTransaction>()
                .Property(b => b.Amount)
                .HasPrecision(18, 2);
        }

        public override int SaveChanges()
        {
            UpdateTimestamps();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateTimestamps();
            return await base.SaveChangesAsync(cancellationToken);
        }

        private void UpdateTimestamps()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.Entity is BaseEntity && (e.State == EntityState.Added || e.State == EntityState.Modified));

            foreach (var entry in entries)
            {
                var entity = (BaseEntity)entry.Entity;

                if (entry.State == EntityState.Added)
                {
                    entity.CreatedAt = DateTime.UtcNow;
                }
                else if (entry.State == EntityState.Modified)
                {
                    entity.UpdatedAt = DateTime.UtcNow;
                }
            }
        }
    }
}
