# MonoovaPayment - Postman Testing Guide

## Prerequisites

1. **Start your application** first:
   ```bash
   dotnet run
   ```
   
2. **Note the port** your application is running on (usually shown in console output like `https://localhost:7000`)

3. **Create the database tables** by running Entity Framework migrations:
   ```bash
   dotnet ef migrations add InitialCreate
   dotnet ef database update
   ```

4. **Create the stored procedures** by running the SQL scripts in your database.

## Import Postman Collection

1. Open Postman
2. Click **Import** button
3. Select the file: `MonoovaPayment_StoredProcedures.postman_collection.json`
4. The collection will be imported with all endpoints ready to test

## Update Base URL

1. In Postman, go to the **MonoovaPayment - Stored Procedures** collection
2. Click on **Variables** tab
3. Update the `baseUrl` variable to match your application's URL (e.g., `https://localhost:7000`)

## Available Endpoints

### 1. GET - Get Transactions by Date Range
- **URL**: `{{baseUrl}}/api/StoredProcedure/transactions`
- **Method**: GET
- **Query Parameters**:
  - `startDate`: 2024-01-01
  - `endDate`: 2024-12-31
- **Example**: `https://localhost:7000/api/StoredProcedure/transactions?startDate=2024-01-01&endDate=2024-12-31`

### 2. GET - Get Payment Summary by Account
- **URL**: `{{baseUrl}}/api/StoredProcedure/payment-summary/{accountId}`
- **Method**: GET
- **Path Parameter**: `accountId` (integer)
- **Example**: `https://localhost:7000/api/StoredProcedure/payment-summary/1`

### 3. GET - Get Card Payment Statistics
- **URL**: `{{baseUrl}}/api/StoredProcedure/card-payment-stats`
- **Method**: GET
- **Query Parameters**:
  - `fromDate`: 2024-01-01
  - `toDate`: 2024-12-31
- **Example**: `https://localhost:7000/api/StoredProcedure/card-payment-stats?fromDate=2024-01-01&toDate=2024-12-31`

### 4. GET - Get Account Balance Summary
- **URL**: `{{baseUrl}}/api/StoredProcedure/account-balance-summary`
- **Method**: GET
- **Example**: `https://localhost:7000/api/StoredProcedure/account-balance-summary`

### 5. POST - Process Bulk Payment Updates
- **URL**: `{{baseUrl}}/api/StoredProcedure/bulk-payment-updates`
- **Method**: POST
- **Headers**: `Content-Type: application/json`
- **Body** (JSON):
```json
{
  "paymentIds": [1, 2, 3, 4, 5],
  "status": "Completed"
}
```

### 6. POST - Archive Old Transactions
- **URL**: `{{baseUrl}}/api/StoredProcedure/archive-transactions`
- **Method**: POST
- **Headers**: `Content-Type: application/json`
- **Body** (JSON):
```json
{
  "cutoffDate": "2023-12-31T23:59:59Z"
}
```

## Testing Steps

### Step 1: Test Basic Connectivity
1. Start with the **Get Account Balance Summary** endpoint (no parameters needed)
2. Send the request
3. You should get a 200 OK response (even if data is empty)

### Step 2: Test with Sample Data
Before testing endpoints that return data, you may need to add some sample data to your database.

### Step 3: Test Each Endpoint
1. **Get Transactions by Date Range**: Adjust dates as needed
2. **Get Payment Summary**: Use an existing account ID
3. **Get Card Payment Statistics**: Adjust date range
4. **Process Bulk Payment Updates**: Use existing payment IDs
5. **Archive Old Transactions**: Use a safe cutoff date

## Common Issues and Solutions

### Issue 1: 404 Not Found
- **Solution**: Check if your application is running and the base URL is correct

### Issue 2: 500 Internal Server Error
- **Solution**: Check if:
  - Database connection is working
  - Stored procedures are created in the database
  - Required services are registered in Program.cs

### Issue 3: Database Connection Issues
- **Solution**: Verify your connection string in `appsettings.json`

### Issue 4: Stored Procedure Not Found
- **Solution**: Run the SQL scripts from `Database/StoredProcedures.sql` in your database

## Sample Responses

### Successful Response (200 OK):
```json
[
  {
    "id": 1,
    "uniqueReference": "TXN-001",
    "amount": 100.50,
    "currency": "AUD",
    "status": "Completed",
    "createdAt": "2024-01-15T10:30:00Z"
  }
]
```

### Error Response (500 Internal Server Error):
```json
{
  "error": "Internal server error"
}
```

## Tips for Testing

1. **Start Simple**: Test GET endpoints first before POST endpoints
2. **Check Logs**: Monitor your application console for error messages
3. **Use Valid Data**: Ensure date formats are correct (YYYY-MM-DD)
4. **Test Edge Cases**: Try with empty results, invalid IDs, etc.
5. **Monitor Database**: Check if stored procedures are actually being called

## Next Steps

After successful testing:
1. Add authentication if needed
2. Implement proper error handling
3. Add input validation
4. Create unit tests
5. Add logging and monitoring
